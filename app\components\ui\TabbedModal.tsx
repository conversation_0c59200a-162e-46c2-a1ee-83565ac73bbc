'use client';

import React, { useState, useEffect, useRef } from 'react';

interface Tab {
  id: string;
  label: string;
  icon?: string;
  content: React.ReactNode;
  disabled?: boolean;
}

interface TabbedModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  tabs: Tab[];
  defaultTab?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  height?: 'auto' | 'fixed' | 'full';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  headerActions?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  onTabChange?: (tabId: string) => void;
}

export default function TabbedModal({
  isOpen,
  onClose,
  title,
  subtitle,
  tabs,
  defaultTab,
  size = 'xl',
  height = 'fixed',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  headerActions,
  footer,
  className = '',
  onTabChange,
}: TabbedModalProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id || '');
  const modalRef = useRef<HTMLDivElement>(null);

  // Update active tab when defaultTab changes
  useEffect(() => {
    if (defaultTab) {
      setActiveTab(defaultTab);
    }
  }, [defaultTab]);

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Handle body scroll lock
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle overlay click
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  // Size configurations
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-2xl',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    full: 'max-w-[95vw]',
  };

  // Height configurations
  const heightClasses = {
    auto: 'max-h-[90vh]',
    fixed: 'h-[85vh]',
    full: 'h-[95vh]',
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300"
        onClick={handleOverlayClick}
      />
      
      {/* Modal Container */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div
          ref={modalRef}
          className={`
            relative bg-white rounded-2xl shadow-2xl border border-gray-200
            w-full ${sizeClasses[size]} ${heightClasses[height]}
            flex flex-col overflow-hidden
            transform transition-all duration-300 ease-out
            ${className}
          `}
        >
          {/* Header */}
          {(title || subtitle || showCloseButton || headerActions) && (
            <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200 bg-white">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  {title && (
                    <h2 className="text-xl font-semibold text-gray-900 truncate">
                      {title}
                    </h2>
                  )}
                  {subtitle && (
                    <p className="mt-1 text-sm text-gray-600 truncate">
                      {subtitle}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {headerActions}
                  {showCloseButton && (
                    <button
                      onClick={onClose}
                      className="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                      aria-label="Close modal"
                    >
                      <i className="ri-close-line text-xl"></i>
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Tab Navigation */}
          {tabs.length > 1 && (
            <div className="flex-shrink-0 bg-white border-b border-gray-200">
              <div className="px-6">
                <nav className="flex space-x-1 overflow-x-auto">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => !tab.disabled && handleTabChange(tab.id)}
                      disabled={tab.disabled}
                      className={`
                        relative px-4 py-3 font-medium text-sm transition-all duration-200 
                        whitespace-nowrap border-b-2 min-w-max
                        ${activeTab === tab.id
                          ? 'text-blue-600 border-blue-600 bg-blue-50/50'
                          : tab.disabled
                          ? 'text-gray-400 border-transparent cursor-not-allowed'
                          : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center">
                        {tab.icon && (
                          <i className={`${tab.icon} mr-2 text-base`}></i>
                        )}
                        {tab.label}
                      </div>
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          )}

          {/* Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
            <div className="h-full">
              {activeTabContent}
            </div>
          </div>

          {/* Footer */}
          {footer && (
            <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-white">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
