import { Pagination } from "../model/common.model";

export interface HotelListApiResponse {
  items: Hotel[];
  pagination: Pagination;
}  

export interface Hotel {
  id: number;
  hotel_id: string;
  name: string;
  city: string;
  country: string;
  country_code: string;
  rating_score: string;
  address: string;
  fax: null | string;
  phones_json: string[];
  faxes_json: null | string[];
  attributes_json: {
    key: string;
    value: string;
  }[];
  longitude: string;
  latitude: string;
  about: null | string;
  status: string;
  type: string;
  chain_code: string;
  provider_id: string;
  provider_hotel_id: string;
  provider_name: string;
  check_in_time: null | string;
  check_out_time: null | string;
  created_at: string;
  updated_at: string;
}

export interface hotelFilters {
    country_code?: string;
    provider_id?: string;
    provider_hotel_id?: string;
    search?: string;
    status?: string;
}


// Interfaces for nested objects
export interface GeoLocationInfo {
    lat: number;
    lon: number;
}

export interface RoomDetails {
    type: string | null;
    bedroom: string | null;
    livingRoom: string | null;
    bathroom: string | null;
    size: string | null;
    bed: string | null;
}

export interface Attribute {
    id: number;
    attribute_key: string;
    attribute_value: string;
    attribute_category: string;
}

export interface AttributesJson {
    key: string;
    value: string;
}

export interface DescriptionContent {
    title: string;
    description: string;
    nearby?: string[];
    amenities?: string[];
    highlights?: string[];
}

export interface Description {
    id: number;
    description_type: string;
    content: DescriptionContent;
    language: string;
}

export interface FacilityDetail {
    id: number;
    content: string;
}

export interface Facility {
    id: number;
    name: string;
    details: FacilityDetail[];
}

export interface Review {
    id: number;
    rating: number;
    content: string;
    type: string;
}

export interface Policy {
    id: number;
    name: string;
    description: string;
}

export interface Image {
    id: number;
    image_path: string;
    alt_text: string;
    image_category_type: string;
    is_hero_image: boolean;
    image_width: number;
    image_height: number;
    sort_order: number;
}

export interface RecentPricing {
    id: number;
    provider_name: string;
    base_rate: number;
    total_rate: number;
    currency_code: string;
    refundable: boolean;
    board_basis: string;
}

// The main Hotel model
export interface hotelDetail {
    id: number;
    hotel_id: string;
    name: string;
    city: string;
    country: string;
    userRating: number | null;
    userRatingCategoty: string | null;
    address: string;
    stateName: string;
    roomDetails: RoomDetails;
    comfortRating: number | null;
    geoLocationInfo: GeoLocationInfo;
    about: string | null;
    HotelType: string;
    starRating: string;
    category: string;
    amenities: string[];
    attributes: Attribute[];
    roomCountLeft: number | null;
    isVisible: boolean;
    chain_code: string;
    chain_name: string;
    data_source_provider: string;
    data_source_updated_at: string | null;
    provider_id: string;
    provider_name: string;
    provider_hotel_id: string;
    hero_image: string;
    image_count: string;
    language: string;
    relevance_score: string;
    phones_json: string[];
    faxes_json: string[] | null;
    attributes_json: AttributesJson[];
    available_suppliers: string[];
    descriptions: Description[];
    facilities: Facility[];
    reviews: Review[];
    policies: Policy[];
    images: Image[];
    // additional_information: any[];
    recent_pricing: RecentPricing[];
}
