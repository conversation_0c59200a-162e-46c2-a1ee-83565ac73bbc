'use client';

import React from 'react';

export default function DashboardHeader() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1">Executive Dashboard</h1>
          <p className="text-slate-600 text-sm sm:text-base">Comprehensive overview of your travel platform operations</p>
        </div>
        <div className="flex items-center gap-3">
          <button className="px-3 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium whitespace-nowrap">
            <i className="ri-download-line mr-2"></i>
            Export Data
          </button>
          <button className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium whitespace-nowrap">
            <i className="ri-add-line mr-2"></i>
            Quick Action
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 sm:p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-blue-100 text-xs sm:text-sm font-medium">Live Performance</p>
              <p className="text-xl sm:text-2xl font-bold truncate">98.7%</p>
              <p className="text-blue-100 text-xs sm:text-sm">System Uptime</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3">
              <i className="ri-pulse-line text-xl sm:text-2xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-xl p-4 sm:p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-emerald-100 text-xs sm:text-sm font-medium">Active Sessions</p>
              <p className="text-xl sm:text-2xl font-bold truncate">2,847</p>
              <p className="text-emerald-100 text-xs sm:text-sm">Current Users</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-emerald-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3">
              <i className="ri-user-line text-xl sm:text-2xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl p-4 sm:p-6 text-white sm:col-span-2 lg:col-span-1">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-purple-100 text-xs sm:text-sm font-medium">Today's Revenue</p>
              <p className="text-xl sm:text-2xl font-bold truncate">$89.2K</p>
              <p className="text-purple-100 text-xs sm:text-sm">+12.3% vs yesterday</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3">
              <i className="ri-trending-up-line text-xl sm:text-2xl"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
