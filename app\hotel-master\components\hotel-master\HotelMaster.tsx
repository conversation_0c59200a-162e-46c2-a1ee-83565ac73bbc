'use client';

import { useState, useEffect, useCallback } from 'react';
import HotelAddEdit from './components/HotelAddEdit';
import HotelList from './components/HotelList';
import HotelView from './components/hotel-master-view/HotelView';
import EnhancedFilters from './components/EnhancedFilters';
import Modal from '../../../components/ui/Modal';
import { getAllHotels, createHotel, updateHotel, getHotelById } from '../../services/hotel.service';
import { Hotel, Pagination } from '../../models/hotel.model';
import { getHotelListApi } from '../../hotel-master-service';

interface HotelMasterProps {
  onHotelSelect: (hotelId: string) => void;
}

export default function HotelMasterOld({ onHotelSelect }: HotelMasterProps) {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingHotelDetails, setLoadingHotelDetails] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState({
    search: '',
    status: '' as '' | 'active' | 'inactive' | 'pending',
    country: '',
    starRating: '',
    contractType: '' as '' | 'static' | 'dynamic',
    provider: '', // Added provider field for enhanced filtering
    country_code: '',
    provider_id: '',
    provider_hotel_id: '',
    has_phone: false,
    has_attributes: false,
  });

  const fetchHotels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Create filter object for enhanced filtering
      const apiFilters = {
        search: filters.search || undefined,
        country_code: filters.country_code || undefined,
        provider_id: filters.provider_id || undefined,
        provider_hotel_id: filters.provider_hotel_id || undefined,
        status: filters.status || undefined,
      };

      const response = await getHotelListApi(currentPage, pageSize, apiFilters);
      setHotels(response?.items || []);
      setPagination(response?.pagination || null);
      setCurrentPage(response?.pagination?.current_page || 1);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels. Please try again.');
      // Fallback to empty array if API fails
      setHotels([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, filters]);

  useEffect(() => {
    fetchHotels();
  }, [fetchHotels]);

  const handleCreateHotel = () => {
    setSelectedHotel(null);
    setIsFormOpen(true);
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    setCurrentPage(1); // Reset to first page when applying filters
    // fetchHotels will be called automatically due to useEffect dependency
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      status: '' as '' | 'active' | 'inactive' | 'pending',
      country: '',
      starRating: '',
      contractType: '' as '' | 'static' | 'dynamic',
      provider: '',
      country_code: '',
      provider_id: '',
      provider_hotel_id: '',
      has_phone: false,
      has_attributes: false,
    });
    setCurrentPage(1);
    // fetchHotels will be called automatically due to useEffect dependency
  };

  const handleEditHotel = (hotel: Hotel) => {
    setSelectedHotel(hotel);
    setIsFormOpen(true);
  };

  const handleViewHotel = async (hotel: Hotel) => {
    setIsViewMode(true);
    setIsFormOpen(true);
    setSelectedHotel(hotel);
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setIsViewMode(false);
    setSelectedHotel(null);
  };

  const handleFormSave = async (hotelData: Partial<Hotel>) => {
    try {
      if (selectedHotel) {
        // Update existing hotel - convert to UpdateHotelRequest
        const updateRequest = {
          name: hotelData.name,
          country: hotelData.country,
          city: hotelData.city,
          area: hotelData.area,
          starRating: hotelData.starRating,
          status: hotelData.status,
          contractType: hotelData.contractType,
          hotelType: hotelData.hotelType,
          provider: hotelData.provider,
          contactDetails: hotelData.contactDetails,
          geolocation: hotelData.geolocation,
          facilities: hotelData.facilities,
          checkInTime: hotelData.checkInTime,
          checkOutTime: hotelData.checkOutTime,
          cancellationPolicy: hotelData.cancellationPolicy,
          gallery: hotelData.gallery,
          about: hotelData.about || undefined, // Convert null to undefined
          phones: hotelData.phones,
          faxes: hotelData.faxes,
          chainCode: hotelData.chainCode,
          providerId: hotelData.providerId,
          providerName: hotelData.providerName,
          providerHotelId: hotelData.providerHotelId,
        };
        const updatedHotel = await updateHotel(selectedHotel.id.toString(), updateRequest);
        setHotels(prevHotels =>
          prevHotels.map(h =>
            h.id === selectedHotel.id ? updatedHotel : h
          )
        );
      } else {
        // Create new hotel - convert to CreateHotelRequest
        const createRequest = {
          name: hotelData.name || '',
          country: hotelData.country || '',
          city: hotelData.city || '',
          area: hotelData.area || '',
          starRating: hotelData.starRating || 0,
          status: hotelData.status || 'active',
          contractType: hotelData.contractType || 'dynamic',
          hotelType: hotelData.hotelType || '',
          contactDetails: hotelData.contactDetails || {
            address: '',
            phone: '',
            email: ''
          },
          geolocation: hotelData.geolocation || {
            latitude: 0,
            longitude: 0
          },
          facilities: hotelData.facilities || [],
          checkInTime: hotelData.checkInTime || '15:00',
          checkOutTime: hotelData.checkOutTime || '12:00',
          cancellationPolicy: hotelData.cancellationPolicy || ''
        };
        const newHotel = await createHotel(createRequest);
        setHotels(prevHotels => [newHotel, ...prevHotels]);
      }
      handleFormClose();
      // No need to call fetchHotels() as local state is already updated
    } catch (error) {
      console.error('Error saving hotel:', error);
      // Handle error - could show a toast notification
    }
  };



  const filteredHotels = (hotels || []).filter(hotel => {
    return (
      (filters.search === '' ||
       hotel.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
       hotel.city?.toLowerCase().includes(filters.search.toLowerCase()) ||
       hotel.country?.toLowerCase().includes(filters.search.toLowerCase())) &&
      (filters.status === '' || hotel.status === filters.status) &&
      (filters.country === '' || hotel.country === filters.country) &&
      (filters.starRating === '' || (hotel.starRating && hotel.starRating.toString() === filters.starRating)) &&
      (filters.contractType === '' || hotel.contractType === filters.contractType)
    );
  });

  return (
    <div className="space-y-6">
      {/* Responsive Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Hotel Master
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {selectedHotel ? 'Manage hotels for selected property' : 'Manage all hotel properties and partnerships'}
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <div className="bg-blue-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-blue-700">
                Total Hotels: {pagination?.total_items || filteredHotels.length}
              </span>
            </div>
            <button
              onClick={handleCreateHotel}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              <i className="ri-add-line mr-2"></i>
              Add Hotel
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Filters */}
      <EnhancedFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
      />

      {/* Responsive Hotel List */}
      <div>
        <HotelList
          hotels={filteredHotels}
          onEdit={handleEditHotel}
          onView={handleViewHotel}
          pagination={pagination}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          loading={loading}
        />
      </div>

      {/* Hotel View Modal */}
      {isFormOpen && isViewMode && (
        <>
          {loadingHotelDetails ? (
            <Modal
              isOpen={isFormOpen && isViewMode}
              onClose={handleFormClose}
              title="Loading Hotel Details"
              size="md"
            >
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading hotel information...</p>
                </div>
              </div>
            </Modal>
          ) : selectedHotel ? (
            <HotelView
              hotel={selectedHotel}
              isOpen={isFormOpen && isViewMode}
              onClose={handleFormClose}
              onEdit={(hotel) => {
                setIsViewMode(false);
                setSelectedHotel(hotel);
              }}

            />
          ) : null}
        </>
      )}

      {/* Hotel Form Modal */}
      {isFormOpen && !isViewMode && (
        <Modal
          isOpen={isFormOpen && !isViewMode}
          onClose={handleFormClose}
          title={selectedHotel ? 'Edit Hotel' : 'Add New Hotel'}
          size="xl"
          height="fixed"
          footer={
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-500">
                <i className="ri-save-line mr-1"></i>
                Auto-saved
              </div>
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={handleFormClose}
                  className="inline-flex items-center px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  form="hotel-form"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  {selectedHotel ? 'Update Hotel' : 'Create Hotel'}
                </button>
              </div>
            </div>
          }
        >
          <HotelAddEdit
            hotel={selectedHotel}
            onSave={handleFormSave}
            onCancel={handleFormClose}
            mode={selectedHotel ? 'edit' : 'add'}
          />
        </Modal>
      )}
    </div>
  );
}
