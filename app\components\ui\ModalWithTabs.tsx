'use client';

import React, { useState, useEffect, Children, isValidElement, FormEvent } from 'react';

export interface TabDefinition {
  id: string;
  label: string;
  icon?: string;
}

interface TabContentProps {
  tabId: string;
  children: React.ReactNode;
}

const TabContent: React.FC<TabContentProps> = ({ children }) => {
  return <>{children}</>;
};

interface TabbedModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  subtitle?: string;
  headerActions?: React.ReactNode;
  tabs: TabDefinition[];
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children: React.ReactNode;
  initialTab?: string;
  formId?: string; // <-- ADDED THIS LINE
  onSubmit?: (e: FormEvent<HTMLFormElement>) => void; // <-- ADDED THIS LINE
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

type TabbedModalComponent = React.FC<TabbedModalProps> & {
  Content: React.FC<TabContentProps>;
};

const TabbedModal: TabbedModalComponent = ({
  isOpen,
  onClose,
  title,
  subtitle,
  headerActions,
  tabs = [],
  footer,
  size = 'lg',
  children,
  initialTab,
  formId,
  onSubmit,
  closeOnOverlayClick = true,
  closeOnEscape = true,
}) => {
  const [activeTab, setActiveTab] = useState(initialTab || (tabs.length > 0 ? tabs[0].id : ''));

  useEffect(() => {
    if (!isOpen) return;
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && closeOnEscape) onClose();
    };
    document.addEventListener('keydown', handleEscape);
    document.body.style.overflow = 'hidden';
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, closeOnEscape, onClose]);
  
  // This useEffect now correctly only runs when the modal opens
  useEffect(() => {
    if (isOpen) {
        setActiveTab(initialTab || (tabs.length > 0 ? tabs[0].id : ''));
    }
  }, [isOpen, initialTab,tabs]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) onClose();
  };

  if (!isOpen) return null;

  const sizeClasses = { sm: 'max-w-md', md: 'max-w-2xl', lg: 'max-w-4xl', xl: 'max-w-6xl', full: 'max-w-[95vw]' };
  
  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={handleOverlayClick} />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div className={`relative bg-white rounded-2xl shadow-2xl border border-gray-200 w-full ${sizeClasses[size]} h-[85vh] flex flex-col overflow-hidden`}>
          <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h2 className="text-lg font-semibold text-gray-900 truncate">{title}</h2>
                {subtitle && <p className="text-sm text-gray-600 mt-1 truncate">{subtitle}</p>}
              </div>
              <div className="ml-4 flex-shrink-0 flex items-center space-x-2">
                {headerActions}
                <button onClick={onClose} className="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
                  <i className="ri-close-line text-xl"></i>
                </button>
              </div>
            </div>
          </div>
          {tabs.length > 0 && (
            <div className="flex-shrink-0 border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button key={tab.id} onClick={() => setActiveTab(tab.id)} className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap transition-colors ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                    {tab.icon && <i className={`${tab.icon} mr-2 text-base`}></i>}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          )}
          <form id={formId} onSubmit={onSubmit} className="flex-1 overflow-y-auto p-6 custom-scrollbar">
            {Children.map(children, child => {
              if (!isValidElement<TabContentProps>(child)) return null;
              const isVisible = child.props.tabId === activeTab;
              return (
                <div style={{ display: isVisible ? 'block' : 'none' }}>
                  {child}
                </div>
              );
            })}
          </form>
          {footer && (
            <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

TabbedModal.Content = TabContent;

export default TabbedModal;