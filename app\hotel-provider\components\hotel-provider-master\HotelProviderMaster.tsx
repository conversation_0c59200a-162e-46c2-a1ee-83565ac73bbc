import { HeaderCards, StatCardProps } from '@/app/components/ui/HeaderCards';
import PageSectionHeader from '@/app/components/ui/PageSectionHeader'
import React, { useMemo, useState } from 'react'
import { HotelProviderFilter, HotelProviderFilterState } from './components/HotelProviderFilter';
import HotelProviderList from './components/HotelProviderList';
import { hotelProvider } from '../../hotel-provider.model';

function HotelProviderMaster() {
    const [isCreateProviderOpen, setIsCreateProviderOpen] = useState<boolean>(false);
    const [providers, setProviders] = useState<hotelProvider[]>([]);
    const [filteredProviders, setFilteredProviders] = useState<hotelProvider[]>(providers);
    const [filters, setFilters] = useState<HotelProviderFilterState>({
        search: '',
        status: '',
    });

    const handleFilterChange = (newFilters: HotelProviderFilterState) => {
        setFilters(newFilters);

        const newFilteredList = providers.filter((provider) => {
        const searchMatch = provider.name
            .toLowerCase()
            .includes(newFilters.search.toLowerCase());
        const statusMatch = newFilters.status
            ? provider.status === newFilters.status
            : true;
        return searchMatch && statusMatch;
        });

        setFilteredProviders(newFilteredList);
    };

    const handleDeleteProvider = (provider: hotelProvider) => {
        setIsCreateProviderOpen(true);
    };

    const handleEditProvider = (provider: hotelProvider) => {
        setIsCreateProviderOpen(true);
    };


    const cardItems: StatCardProps[] = [
        {
        title: 'Total Providers',
        value: providers.length,
        icon: 'ri-building-2-fill',
        color: 'blue',
        description: 'All registered providers',
        subDescription: 'Across all channels',
        change: { value: 5.2 },
        },
        {
        title: 'Active Providers',
        value: providers.filter(p => p.status === 'active').length,
        icon: 'ri-checkbox-circle-fill',
        color: 'emerald',
        description: 'Currently operational',
        subDescription: providers.length > 0 ? `${((providers.filter(p => p.status === 'active').length / providers.length) * 100).toFixed(0)}% of total` : '0% of total',
        change: { value: 8.1 },
        },
        {
        title: 'Inactive Providers',
        value: providers.filter(p => p.status === 'inactive').length,
        icon: 'ri-pause-circle-fill',
        color: 'amber',
        description: 'Temporarily disabled',
        subDescription: 'Need attention',
        change: { value: -2.3 },
        },
        {
        title: 'Recently Updated',
        value: providers.filter(p => {
            const updatedDate = new Date(p.updatedAt);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return updatedDate > weekAgo;
        }).length,
        icon: 'ri-time-fill',
        color: 'purple',
        description: 'Updated in the last 7 days',
        subDescription: 'Configuration changes',
        change: { value: 12.4 },
        },
    ];

  return (
    <div className='space-y-6'>

        {/* header */}
        <PageSectionHeader 
          title='Hotel Providers' 
          subtitle='Manage hotel provider integrations and API connections' 
          totalItems={0} 
          showAddButton={true} 
          addButtonText='Add Provider'
          onAddButtonClick={() => setIsCreateProviderOpen(true)}
        />

        {/* header cards */}
        <HeaderCards items={cardItems}/>

        {/* filters */}
        <HotelProviderFilter
            filters={filters}
            onFilterChange={handleFilterChange}
        />

        {/* list */}
        <HotelProviderList onEdit={handleEditProvider} onDelete={handleDeleteProvider} providers={filteredProviders} />

        {/* modals */}
        
    </div>
  )
}

export default HotelProviderMaster