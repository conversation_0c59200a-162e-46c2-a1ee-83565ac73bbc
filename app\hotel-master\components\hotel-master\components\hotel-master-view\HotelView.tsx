'use client';

import React, { useState, useEffect } from 'react';
import TabbedModal from '../../../../../components/ui/TabbedModal';
import { Hotel } from '../../../../models/hotel.model';
import { FacilityType, getFacilityTypesByHotel } from '../../../../services/facilities.service';
import { HotelReview, HotelPolicy, getHotelReviews, getHotelPolicies } from '../../../../services/reviews-policies.service';
import { HotelImage, getHotelImages } from '../../../../services/images-information.service';



interface HotelViewProps {
  hotel: Hotel | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (hotel: Hotel) => void;
}

export default function HotelView({ hotel, isOpen, onClose, onEdit }: HotelViewProps) {

  // New state for additional data
  const [facilities, setFacilities] = useState<FacilityType[]>([]);
  const [reviews, setReviews] = useState<HotelReview[]>([]);
  const [policies, setPolicies] = useState<HotelPolicy[]>([]);
  const [images, setImages] = useState<HotelImage[]>([]);
  const [loadingFacilities, setLoadingFacilities] = useState(false);
  const [loadingReviews, setLoadingReviews] = useState(false);
  const [loadingPolicies, setLoadingPolicies] = useState(false);
  const [loadingImages, setLoadingImages] = useState(false);

  if (!hotel) return null;

  // Fetch additional hotel data when hotel changes
  useEffect(() => {
    if (hotel && isOpen) {
      fetchHotelData();
    }
  }, [hotel?.id, isOpen]);

  const fetchHotelData = async () => {
    const hotelId = hotel.id.toString();

    // Fetch facilities
    setLoadingFacilities(true);
    try {
      const facilitiesData = await getFacilityTypesByHotel(hotelId);
      setFacilities(facilitiesData);
    } catch (error) {
      console.error('Error fetching facilities:', error);
      setFacilities([]);
    } finally {
      setLoadingFacilities(false);
    }



    // Fetch reviews
    setLoadingReviews(true);
    try {
      const reviewsData = await getHotelReviews(hotelId);
      setReviews(reviewsData);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      setReviews([]);
    } finally {
      setLoadingReviews(false);
    }

    // Fetch policies
    setLoadingPolicies(true);
    try {
      const policiesData = await getHotelPolicies(hotelId);
      setPolicies(policiesData);
    } catch (error) {
      console.error('Error fetching policies:', error);
      setPolicies([]);
    } finally {
      setLoadingPolicies(false);
    }

    // Fetch images
    setLoadingImages(true);
    try {
      const imagesData = await getHotelImages(hotelId);
      console.log('Fetched images data:', imagesData); // Debug log
      setImages(imagesData);
    } catch (error) {
      console.error('Error fetching images:', error);
      setImages([]);
    } finally {
      setLoadingImages(false);
    }
  };



  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: 'ri-check-line', label: 'Active' },
      inactive: { color: 'bg-red-100 text-red-800', icon: 'ri-close-line', label: 'Inactive' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: 'ri-time-line', label: 'Pending' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {config.label}
      </span>
    );
  };

  const getContractTypeBadge = (type: string) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        type === 'static' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
      }`}>
        <i className={`${type === 'static' ? 'ri-lock-line' : 'ri-refresh-line'} mr-1`}></i>
        {type === 'static' ? 'Static' : 'Dynamic'}
      </span>
    );
  };

  const getFacilityIcon = (category: string) => {
    const iconMap = {
      amenity: 'ri-star-line',
      service: 'ri-customer-service-line',
      recreation: 'ri-gamepad-line',
      business: 'ri-briefcase-line',
      accessibility: 'ri-wheelchair-line'
    };
    return iconMap[category as keyof typeof iconMap] || 'ri-check-line';
  };



  // Define tabs for the modal
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="p-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Information */}
              <div className="lg:col-span-2 space-y-6">
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i className="ri-building-line text-blue-600 text-lg"></i>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Hotel Information</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Hotel Name</label>
                        <p className="text-gray-900 font-medium">{hotel.name || 'Unnamed Hotel'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Location</label>
                        <p className="text-gray-900">{hotel.address || `${hotel.city}, ${hotel.country}`}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Hotel Type</label>
                        <p className="text-gray-900">{hotel.hotelType || hotel.type || 'N/A'}</p>
                      </div>
                      {hotel.provider_name && (
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">Provider</label>
                          <div className="flex items-center space-x-2">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {hotel.provider_name}
                            </span>
                            {hotel.provider_hotel_id && (
                              <span className="text-xs text-gray-500">ID: {hotel.provider_hotel_id}</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Star Rating</label>
                        <div className="flex items-center space-x-1">
                          {[...Array(5)].map((_, i) => {
                            const rating = hotel.starRating || (hotel.rating_score ? parseInt(hotel.rating_score) : 0);
                            return (
                              <i key={i} className={`ri-star-${i < rating ? 'fill' : 'line'} text-amber-400`}></i>
                            );
                          })}
                          <span className="ml-2 text-gray-600">({hotel.starRating || hotel.rating_score || 'N/A'}/5)</span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                        {getStatusBadge(hotel.status || 'pending')}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Contract Type</label>
                        {getContractTypeBadge(hotel.contractType || 'static')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                {(hotel.contactDetails || hotel.address || hotel.phones_json || hotel.fax) && (
                  <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <div className="flex items-center mb-6">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i className="ri-phone-line text-green-600 text-lg"></i>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {(hotel.contactDetails?.address || hotel.address) && (
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">Address</label>
                          <p className="text-gray-900">{hotel.contactDetails?.address || hotel.address}</p>
                        </div>
                      )}
                      {(hotel.contactDetails?.phone || hotel.phones_json?.[0]) && (
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                          <p className="text-gray-900">{hotel.contactDetails?.phone || hotel.phones_json?.[0]}</p>
                        </div>
                      )}
                      {hotel.contactDetails?.email && (
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">Email</label>
                          <p className="text-gray-900">{hotel.contactDetails.email}</p>
                        </div>
                      )}
                      {hotel.contactDetails?.website && (
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">Website</label>
                          <a href={hotel.contactDetails.website} target="_blank" rel="noopener noreferrer"
                             className="text-blue-600 hover:text-blue-800 underline">
                            {hotel.contactDetails.website}
                          </a>
                        </div>
                      )}
                      {hotel.fax && (
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">Fax</label>
                          <p className="text-gray-900">{hotel.fax}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                  <div className="space-y-4">
                    {hotel.phones_json && hotel.phones_json.length > 0 && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Phone</span>
                        <span className="font-semibold text-gray-900">{hotel.phones_json[0]}</span>
                      </div>
                    )}
                    {hotel.country_code && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Country Code</span>
                        <span className="font-semibold text-gray-900">{hotel.country_code}</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Provider</span>
                      <span className="font-semibold text-gray-900">{hotel.provider || hotel.provider_name || 'Direct'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Created</span>
                      <span className="font-semibold text-gray-900">
                        {hotel.createdAt ? new Date(hotel.createdAt).toLocaleDateString() :
                         hotel.created_at ? new Date(hotel.created_at).toLocaleDateString() : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Last Updated</span>
                      <span className="font-semibold text-gray-900">{hotel.updatedAt}</span>
                    </div>
                  </div>
                </div>

                {/* Facilities */}
                {hotel.facilities && hotel.facilities.length > 0 && (
                  <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Facilities</h3>
                    <div className="flex flex-wrap gap-2">
                      {(hotel.facilities || []).map((facility, index) => (
                        <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                          {facility}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'facilities',
      label: 'Facilities',
      icon: 'ri-star-line',
      content: (
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-star-line text-amber-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Hotel Facilities & Amenities</h3>
                </div>
                {loadingFacilities && (
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600"></div>
                )}
              </div>

              {loadingFacilities ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Loading facilities...</p>
                  </div>
                </div>
              ) : facilities.length > 0 ? (
                <div className="space-y-6">
                  {/* Display all facilities in a simple grid */}
                  <div className="space-y-3">
                    <h4 className="text-md font-semibold text-gray-800 capitalize border-b border-gray-200 pb-2">
                      Hotel Facilities
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                      {facilities.map((facility) => (
                        <div key={facility.id} className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <i className={`${getFacilityIcon(facility.category || 'amenity')} text-blue-600 mr-2`}></i>
                          <div className="flex-1">
                            <span className="text-sm font-medium text-blue-900">{facility.name || 'Unnamed Facility'}</span>
                            {facility.description && (
                              <p className="text-xs text-blue-700 mt-1">{facility.description}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (hotel.facilities && hotel.facilities.length > 0) ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {(hotel.facilities || []).map((facility, index) => (
                    <div key={index} className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <i className="ri-check-line text-blue-600 mr-2"></i>
                      <span className="text-sm font-medium text-blue-900">{facility}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <i className="ri-information-line text-gray-400 text-3xl mb-3"></i>
                  <p className="text-gray-500 italic">No facilities information available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'policies',
      label: 'Policies',
      icon: 'ri-file-text-line',
      content: (
        <div className="p-6">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Check-in/Check-out Times */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-time-line text-purple-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Check-in & Check-out</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Check-in Time</label>
                  <p className="text-gray-900 font-medium">
                    {(() => {
                      const checkInPolicy = policies.find(p => p.name === 'check_in');
                      return checkInPolicy?.description || hotel.checkInTime || hotel.check_in_time || 'Not specified';
                    })()}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Check-out Time</label>
                  <p className="text-gray-900 font-medium">
                    {(() => {
                      const checkOutPolicy = policies.find(p => p.name === 'check_out');
                      return checkOutPolicy?.description || hotel.checkOutTime || hotel.check_out_time || 'Not specified';
                    })()}
                  </p>
                </div>
              </div>
            </div>

            {/* Hotel Policies */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-file-text-line text-orange-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Hotel Policies</h3>
                </div>
                {loadingPolicies && (
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"></div>
                )}
              </div>

              {loadingPolicies ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Loading policies...</p>
                  </div>
                </div>
              ) : policies.length > 0 ? (
                <div className="space-y-6">
                  {/* Group policies by category */}
                  {(() => {
                    const timeRelatedPolicies = policies.filter(p => p.name === 'check_in' || p.name === 'check_out');
                    const otherPolicies = policies.filter(p => p.name !== 'check_in' && p.name !== 'check_out');

                    return (
                      <>
                        {/* Time-related policies */}
                        {timeRelatedPolicies.length > 0 && (
                          <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
                            <h4 className="text-md font-semibold text-blue-900 mb-3 flex items-center">
                              <i className="ri-time-line mr-2"></i>
                              Check-in & Check-out Times
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {timeRelatedPolicies.map((policy) => (
                                <div key={policy.id} className="bg-white rounded-lg p-3 border border-blue-100">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium text-blue-900 capitalize">
                                      {policy.name.replace('_', ' ')}
                                    </span>
                                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                      {policy.name === 'check_in' ? 'Arrival' : 'Departure'}
                                    </span>
                                  </div>
                                  <p className="text-lg font-semibold text-blue-800">{policy.description}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Other policies */}
                        {otherPolicies.length > 0 && (
                          <div className="space-y-4">
                            <h4 className="text-md font-semibold text-gray-900 flex items-center">
                              <i className="ri-file-text-line mr-2"></i>
                              Hotel Policies & Rules
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {otherPolicies.map((policy) => (
                                <div key={policy.id} className="border border-gray-200 rounded-lg p-4 bg-white">
                                  <div className="flex items-center justify-between mb-3">
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                      policy.name === 'cancellation' ? 'bg-red-100 text-red-800' :
                                      policy.name === 'pets' ? 'bg-green-100 text-green-800' :
                                      policy.name === 'smoking' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {policy.name ? policy.name.replace('_', ' ').toUpperCase() : 'POLICY'}
                                    </span>
                                    <div className="flex items-center text-xs text-gray-500">
                                      <i className="ri-calendar-line mr-1"></i>
                                      {new Date(policy.created_at).toLocaleDateString()}
                                    </div>
                                  </div>

                                  <h5 className="font-semibold text-gray-900 mb-2 capitalize flex items-center">
                                    <i className={`mr-2 text-sm ${
                                      policy.name === 'cancellation' ? 'ri-close-circle-line text-red-500' :
                                      policy.name === 'pets' ? 'ri-heart-line text-green-500' :
                                      policy.name === 'smoking' ? 'ri-forbid-line text-yellow-500' :
                                      'ri-information-line text-gray-500'
                                    }`}></i>
                                    {policy.name?.replace('_', ' ') || 'Policy'}
                                  </h5>
                                  <p className="text-gray-700 text-sm leading-relaxed">{policy.description || 'No description available'}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              ) : hotel.cancellationPolicy ? (
                <div className="prose max-w-none">
                  <h4 className="font-semibold text-gray-900 mb-2">Cancellation Policy</h4>
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{hotel.cancellationPolicy}</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <i className="ri-file-text-line text-gray-400 text-3xl mb-3"></i>
                  <p className="text-gray-500 italic">No policies information available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'reviews',
      label: 'Reviews',
      icon: 'ri-star-line',
      content: (
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-star-line text-yellow-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Guest Reviews</h3>
                </div>
                {loadingReviews && (
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-600"></div>
                )}
              </div>

              {loadingReviews ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Loading reviews...</p>
                  </div>
                </div>
              ) : reviews.length > 0 ? (
                <div className="space-y-6">
                  {/* Reviews Summary */}
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <div className="text-3xl font-bold text-yellow-600">
                            {(reviews.reduce((sum, review) => sum + parseFloat(review.rating || '0'), 0) / reviews.length).toFixed(1)}
                          </div>
                          <div className="flex items-center justify-center space-x-1 mt-1">
                            {[...Array(5)].map((_, i) => (
                              <i key={i} className={`ri-star-${i < Math.round(reviews.reduce((sum, review) => sum + parseFloat(review.rating || '0'), 0) / reviews.length) ? 'fill' : 'line'} text-yellow-500 text-sm`}></i>
                            ))}
                          </div>
                        </div>
                        <div>
                          <p className="text-lg font-semibold text-gray-900">{reviews.length} Reviews</p>
                          <p className="text-sm text-gray-600">
                            {reviews.filter(r => r.status === 'approved').length} approved,
                            {reviews.filter(r => r.verified).length} verified
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Individual Reviews */}
                  <div className="space-y-4">
                    {reviews.slice(0, 5).map((review) => (
                      <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                              <i className="ri-user-line text-gray-600"></i>
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900">{review.guest_name || 'Anonymous Guest'}</h4>
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center space-x-1">
                                  {[...Array(5)].map((_, i) => (
                                    <i key={i} className={`ri-star-${i < parseFloat(review.rating || '0') ? 'fill' : 'line'} text-yellow-500 text-sm`}></i>
                                  ))}
                                </div>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {review.type || 'guest_review'}
                                </span>
                                {review.verified && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i className="ri-check-line mr-1"></i>
                                    Verified
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="text-right text-sm text-gray-500">
                            <div>{review.created_at ? new Date(review.created_at).toLocaleDateString() : 'N/A'}</div>
                          </div>
                        </div>

                        <h5 className="font-medium text-gray-900 mb-2">{review.title || 'Guest Review'}</h5>
                        <p className="text-gray-700 mb-3">{review.content || review.comment || 'No comment provided'}</p>

                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <span>Review date: {review.created_at ? new Date(review.created_at).toLocaleDateString() : 'N/A'}</span>
                          {review.helpful_votes && review.helpful_votes > 0 && (
                            <span className="flex items-center">
                              <i className="ri-thumb-up-line mr-1"></i>
                              {review.helpful_votes} helpful
                            </span>
                          )}
                        </div>
                      </div>
                    ))}

                    {reviews.length > 5 && (
                      <div className="text-center py-4">
                        <p className="text-gray-500">Showing 5 of {reviews.length} reviews</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <i className="ri-star-line text-gray-400 text-3xl mb-3"></i>
                  <p className="text-gray-500 italic">No reviews available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'location',
      label: 'Location',
      icon: 'ri-map-pin-line',
      content: (
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-map-pin-line text-red-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Location Details</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Country</label>
                    <p className="text-gray-900 font-medium">{hotel.country}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">City</label>
                    <p className="text-gray-900 font-medium">{hotel.city}</p>
                  </div>
                  {hotel.area && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Area/District</label>
                      <p className="text-gray-900 font-medium">{hotel.area}</p>
                    </div>
                  )}
                </div>
                <div className="space-y-4">
                  {(hotel.geolocation || hotel.latitude || hotel.longitude) && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Latitude</label>
                        <p className="text-gray-900 font-mono">{hotel.geolocation?.latitude || hotel.latitude || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Longitude</label>
                        <p className="text-gray-900 font-mono">{hotel.geolocation?.longitude || hotel.longitude || 'N/A'}</p>
                      </div>
                    </>
                  )}
                  {(hotel.contactDetails?.address || hotel.address) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Full Address</label>
                      <p className="text-gray-900">{hotel.contactDetails?.address || hotel.address}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'gallery',
      label: 'Gallery',
      icon: 'ri-image-line',
      content: (
        <div className="p-6">
          <div className="max-w-6xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-image-line text-pink-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Hotel Gallery</h3>
                </div>
                {loadingImages && (
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-pink-600"></div>
                )}
              </div>

              {loadingImages ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Loading gallery images...</p>
                  </div>
                </div>
              ) : (
                <>
                  {/* Hotel Gallery Images */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {/* Use API images first, then hotel.gallery, then fallback to sample images */}
                    {(() => {
                      let imagesToShow = [];
                      console.log('Images state:', images); // Debug log

                      if (images.length > 0) {
                        // Use API images
                        console.log('Processing images for display:', images); // Debug log
                        imagesToShow = images
                          .sort((a, b) => a.sort_order - b.sort_order)
                          .map(img => {
                            // Map image_category_type to display type
                            const categoryType = img.image_category_type?.toLowerCase() || 'other';
                            let displayType = 'other';

                            if (categoryType.includes('hero')) displayType = 'exterior';
                            else if (categoryType.includes('exterior')) displayType = 'exterior';
                            else if (categoryType.includes('interior')) displayType = 'interior';
                            else if (categoryType.includes('room')) displayType = 'room';
                            else if (categoryType.includes('dining')) displayType = 'dining';
                            else if (categoryType.includes('recreation') || categoryType.includes('pool')) displayType = 'recreation';
                            else if (categoryType.includes('amenity')) displayType = 'amenity';

                            const mappedImage = {
                              id: img.id,
                              title: img.alt_text || `${img.image_category_type} Image`,
                              url: img.image_path,
                              type: displayType,
                              description: img.alt_text,
                              isPrimary: img.is_hero_image
                            };

                            console.log('Mapped image:', mappedImage); // Debug log
                            return mappedImage;
                          });
                      } else if (hotel.gallery && hotel.gallery.length > 0) {
                        // Use hotel.gallery as fallback
                        imagesToShow = (hotel.gallery || []).map((url, index) => ({
                          id: `gallery-${index}`,
                          title: `Hotel Image ${index + 1}`,
                          url,
                          type: 'other',
                          isPrimary: index === 0,
                          description: undefined
                        }));
                      } else {
                        // Use sample images as final fallback with better URLs
                        imagesToShow = [
                          { id: 'sample-1', title: 'Hotel Exterior', url: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300', type: 'exterior', isPrimary: true, description: undefined },
                          { id: 'sample-2', title: 'Lobby Area', url: 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300', type: 'interior', isPrimary: false, description: undefined },
                          { id: 'sample-3', title: 'Swimming Pool', url: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300', type: 'recreation', isPrimary: false, description: undefined },
                          { id: 'sample-4', title: 'Restaurant', url: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300', type: 'dining', isPrimary: false, description: undefined },
                          { id: 'sample-5', title: 'Spa & Wellness', url: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300', type: 'recreation', isPrimary: false, description: undefined },
                          { id: 'sample-6', title: 'Conference Room', url: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300', type: 'other', isPrimary: false, description: undefined }
                        ];
                      }

                      return imagesToShow.map((image) => (
                        <div key={image.id} className="group relative overflow-hidden rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300">
                          <div className="aspect-w-4 aspect-h-3 bg-gray-100 relative">
                            {/* Loading placeholder */}
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                              <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-2"></div>
                                <p className="text-xs text-gray-500">Loading...</p>
                              </div>
                            </div>

                            <img
                              src={image.url}
                              alt={image.title}
                              className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300 relative z-10"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                console.log('Image failed to load:', image.url); // Debug log
                                // Use a better placeholder with proper colors
                                target.src = `https://via.placeholder.com/400x300/f3f4f6/6b7280?text=${encodeURIComponent(image.title)}`;
                              }}
                              onLoad={(e) => {
                                console.log('Image loaded successfully:', image.url); // Debug log
                                // Hide loading placeholder
                                const target = e.target as HTMLImageElement;
                                const loadingDiv = target.previousElementSibling as HTMLElement;
                                if (loadingDiv) {
                                  loadingDiv.style.display = 'none';
                                }
                              }}
                            />
                          </div>

                          {/* Image type badge */}
                          <div className="absolute top-2 left-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              image.type === 'exterior' ? 'bg-blue-100 text-blue-800' :
                              image.type === 'interior' ? 'bg-green-100 text-green-800' :
                              image.type === 'room' ? 'bg-purple-100 text-purple-800' :
                              image.type === 'dining' ? 'bg-orange-100 text-orange-800' :
                              image.type === 'recreation' ? 'bg-teal-100 text-teal-800' :
                              image.type === 'amenity' ? 'bg-indigo-100 text-indigo-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {image.type || 'Unknown'}
                            </span>
                          </div>

                          {/* Primary badge */}
                          {image.isPrimary && (
                            <div className="absolute top-2 right-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i className="ri-star-fill mr-1"></i>
                                Primary
                              </span>
                            </div>
                          )}

                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <button className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                                <i className="ri-eye-line mr-2"></i>
                                View
                              </button>
                            </div>
                          </div>

                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                            <p className="text-white text-sm font-medium">{image.title}</p>
                            {image.description && (
                              <p className="text-white text-xs opacity-90 mt-1">{image.description}</p>
                            )}
                          </div>
                        </div>
                      ));
                    })()}
                  </div>

                  {/* Gallery info */}
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <i className="ri-information-line text-blue-600 mr-2"></i>
                        <span className="text-sm font-medium text-blue-900">
                          {images.length > 0
                            ? `Showing ${images.length} hotel images from API`
                            : hotel.gallery && hotel.gallery.length > 0
                            ? `Showing ${hotel.gallery.length} hotel gallery images`
                            : 'Showing sample gallery images'
                          }
                        </span>
                      </div>
                      <span className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded">
                        {images.length > 0 ? 'API Images' :
                         hotel.gallery && hotel.gallery.length > 0 ? 'Gallery Images' : 'Sample Images'}
                      </span>
                    </div>

                    {images.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {['hero', 'exterior', 'interior', 'room', 'dining', 'recreation', 'amenity', 'other'].map(type => {
                          const count = images.filter(img => img.image_category_type?.toLowerCase() === type).length;
                          if (count === 0) return null;
                          return (
                            <span key={type} className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                              {type}: {count}
                            </span>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <>
      <TabbedModal
        isOpen={isOpen}
        onClose={onClose}
        title={hotel.name || 'Unnamed Hotel'}
        subtitle={`${hotel.city || 'Unknown City'}, ${hotel.country || 'Unknown Country'} • ${hotel.hotelType || hotel.type || 'Hotel'}`}
        tabs={tabs}
        size="full"
        height="fixed"
        headerActions={
          <div className="flex items-center space-x-3">
            {/* Status Badge */}
            <div className="flex items-center">
              {getStatusBadge(hotel.status || 'pending')}
            </div>

            {/* Star Rating */}
            <div className="flex items-center space-x-1 px-3 py-1 bg-amber-50 rounded-lg border border-amber-200">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => {
                  const rating = hotel.starRating || (hotel.rating_score ? parseInt(hotel.rating_score) : 0);
                  return (
                    <i key={i} className={`ri-star-${i < rating ? 'fill' : 'line'} text-amber-500 text-sm`}></i>
                  );
                })}
              </div>
              <span className="text-sm font-medium text-amber-700 ml-1">
                {hotel.starRating || hotel.rating_score || 'N/A'} Star{(hotel.starRating || parseInt(hotel.rating_score || '0')) !== 1 ? 's' : ''}
              </span>
            </div>

            {/* Provider Badge */}
            {(hotel.provider || hotel.provider_name) && (
              <div className="flex items-center px-3 py-1 bg-gray-100 rounded-lg border border-gray-200">
                <i className="ri-building-2-line text-gray-600 mr-2 text-sm"></i>
                <span className="text-sm font-medium text-gray-700">{hotel.provider || hotel.provider_name}</span>
              </div>
            )}

            {/* Edit Button */}
            {onEdit && (
              <button
                onClick={() => onEdit(hotel)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
              >
                <i className="ri-edit-line mr-2 text-sm"></i>
                Edit Hotel
              </button>
            )}
          </div>
        }
      />


    </>
  );
}
