'use client';

import React from 'react';
import TabbedModal from '../../../components/ui/TabbedModal';
import { Cancellation, getCancellationStatusText, getRefundStatusText, getCancellationReasonText } from '../types';

interface CancellationViewProps {
  cancellation: Cancellation | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function CancellationView({ cancellation, isOpen, onClose }: CancellationViewProps) {
  if (!cancellation) return null;

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string, type: 'cancellation' | 'refund') => {
    const cancellationConfig = {
      'pending': 'bg-amber-100 text-amber-800 border-amber-200',
      'approved': 'bg-blue-100 text-blue-800 border-blue-200',
      'rejected': 'bg-red-100 text-red-800 border-red-200',
      'processed': 'bg-green-100 text-green-800 border-green-200',
      'completed': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'cancelled': 'bg-gray-100 text-gray-800 border-gray-200'
    };

    const refundConfig = {
      'not-applicable': 'bg-gray-100 text-gray-800 border-gray-200',
      'pending': 'bg-amber-100 text-amber-800 border-amber-200',
      'processing': 'bg-blue-100 text-blue-800 border-blue-200',
      'completed': 'bg-green-100 text-green-800 border-green-200',
      'failed': 'bg-red-100 text-red-800 border-red-200',
      'partial': 'bg-orange-100 text-orange-800 border-orange-200',
      'rejected': 'bg-red-100 text-red-800 border-red-200'
    };

    const config = type === 'cancellation' ? cancellationConfig : refundConfig;
    const displayText = type === 'cancellation' ? getCancellationStatusText(status as any) : getRefundStatusText(status as any);

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${config[status as keyof typeof config] || 'bg-gray-100 text-gray-800 border-gray-200'}`}>
        {displayText}
      </span>
    );
  };

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="space-y-8">
          {/* Header Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Cancellation ID</label>
                <p className="text-lg font-semibold text-gray-900">{cancellation.cancellationId}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Booking Reference</label>
                <p className="text-lg font-semibold text-gray-900">{cancellation.bookingReference}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Cancellation Date</label>
                <p className="text-lg font-semibold text-gray-900">{formatDate(cancellation.cancellationDate)}</p>
              </div>
            </div>
          </div>

          {/* Guest Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Guest Information</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Guest Name</label>
                  <p className="text-gray-900 font-medium">{cancellation.guestName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Email</label>
                  <p className="text-gray-900">{cancellation.guestEmail}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                  <p className="text-gray-900">{cancellation.guestPhone}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Number of Guests</label>
                  <p className="text-gray-900">{cancellation.numberOfGuests}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Booking Details</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Hotel</label>
                  <p className="text-gray-900 font-medium">{cancellation.hotelName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Room</label>
                  <p className="text-gray-900">{cancellation.roomName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Check-in Date</label>
                  <p className="text-gray-900">{new Date(cancellation.originalCheckInDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Check-out Date</label>
                  <p className="text-gray-900">{new Date(cancellation.originalCheckOutDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Number of Nights</label>
                  <p className="text-gray-900">{cancellation.numberOfNights}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Cancellation Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancellation Details</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Reason</label>
                  <p className="text-gray-900">{getCancellationReasonText(cancellation.cancellationReason)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Type</label>
                  <p className="text-gray-900 capitalize">{cancellation.cancellationType.replace('-', ' ')}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Requested By</label>
                  <p className="text-gray-900 capitalize">{cancellation.requestedBy.replace('-', ' ')}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Processed By</label>
                  <p className="text-gray-900">{cancellation.processedBy || 'Not assigned'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(cancellation.cancellationStatus, 'cancellation')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'financial',
      label: 'Financial Details',
      icon: 'ri-money-dollar-circle-line',
      content: (
        <div className="space-y-8">
          {/* Financial Summary */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-sm font-medium text-gray-500">Original Amount</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(cancellation.originalAmount, cancellation.currency)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-gray-500">Refund Amount</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(cancellation.refundAmount, cancellation.currency)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-gray-500">Cancellation Fee</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(cancellation.cancellationFee, cancellation.currency)}</p>
              </div>
            </div>
          </div>

          {/* Refund Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Refund Information</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Refund Status</label>
                  <div className="mt-1">
                    {getStatusBadge(cancellation.refundStatus, 'refund')}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Refund Method</label>
                  <p className="text-gray-900 capitalize">{cancellation.refundMethod.replace('-', ' ')}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Currency</label>
                  <p className="text-gray-900">{cancellation.currency}</p>
                </div>
                {cancellation.refundDate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Refund Date</label>
                    <p className="text-gray-900">{formatDate(cancellation.refundDate)}</p>
                  </div>
                )}
                {cancellation.refundTransactionId && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Refund Transaction ID</label>
                    <p className="text-gray-900 font-mono text-sm">{cancellation.refundTransactionId}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Payment Details */}
          {cancellation.paymentDetails && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Details</h3>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {cancellation.paymentDetails.originalTransactionId && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Original Transaction ID</label>
                      <p className="text-gray-900 font-mono text-sm">{cancellation.paymentDetails.originalTransactionId}</p>
                    </div>
                  )}
                  {cancellation.paymentDetails.originalPaymentMethod && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Original Payment Method</label>
                      <p className="text-gray-900">{cancellation.paymentDetails.originalPaymentMethod}</p>
                    </div>
                  )}
                  {cancellation.paymentDetails.cardLast4 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Card Details</label>
                      <p className="text-gray-900">{cancellation.paymentDetails.cardType} ending in {cancellation.paymentDetails.cardLast4}</p>
                    </div>
                  )}
                  {cancellation.paymentDetails.refundReference && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Refund Reference</label>
                      <p className="text-gray-900 font-mono text-sm">{cancellation.paymentDetails.refundReference}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )
    },
    {
      id: 'guest',
      label: 'Guest Details',
      icon: 'ri-user-line',
      content: (
        <div className="space-y-8">
          {/* Guest Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Guest Information</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {cancellation.guestDetails?.firstName && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">First Name</label>
                    <p className="text-gray-900">{cancellation.guestDetails.firstName}</p>
                  </div>
                )}
                {cancellation.guestDetails?.lastName && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Last Name</label>
                    <p className="text-gray-900">{cancellation.guestDetails.lastName}</p>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Email</label>
                  <p className="text-gray-900">{cancellation.guestEmail}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                  <p className="text-gray-900">{cancellation.guestPhone}</p>
                </div>
                {cancellation.guestDetails?.nationality && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Nationality</label>
                    <p className="text-gray-900">{cancellation.guestDetails.nationality}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Address Information */}
          {cancellation.guestDetails?.address && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Address Information</h3>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="space-y-4">
                  {cancellation.guestDetails.address.street && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Street Address</label>
                      <p className="text-gray-900">{cancellation.guestDetails.address.street}</p>
                    </div>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {cancellation.guestDetails.address.city && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">City</label>
                        <p className="text-gray-900">{cancellation.guestDetails.address.city}</p>
                      </div>
                    )}
                    {cancellation.guestDetails.address.state && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">State/Province</label>
                        <p className="text-gray-900">{cancellation.guestDetails.address.state}</p>
                      </div>
                    )}
                    {cancellation.guestDetails.address.country && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">Country</label>
                        <p className="text-gray-900">{cancellation.guestDetails.address.country}</p>
                      </div>
                    )}
                    {cancellation.guestDetails.address.zipCode && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-1">ZIP/Postal Code</label>
                        <p className="text-gray-900">{cancellation.guestDetails.address.zipCode}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )
    },
    {
      id: 'notes',
      label: 'Notes & Policy',
      icon: 'ri-file-text-line',
      content: (
        <div className="space-y-8">
          {/* Cancellation Notes */}
          {cancellation.cancellationNotes && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancellation Notes</h3>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <p className="text-gray-900 whitespace-pre-wrap">{cancellation.cancellationNotes}</p>
              </div>
            </div>
          )}

          {/* Refund Notes */}
          {cancellation.refundNotes && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Refund Notes</h3>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <p className="text-gray-900 whitespace-pre-wrap">{cancellation.refundNotes}</p>
              </div>
            </div>
          )}

          {/* Cancellation Policy */}
          {cancellation.cancellationPolicy && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancellation Policy</h3>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <p className="text-gray-900 whitespace-pre-wrap">{cancellation.cancellationPolicy}</p>
              </div>
            </div>
          )}

          {/* Timeline */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Cancellation Created</p>
                    <p className="text-sm text-gray-500">{formatDate(cancellation.createdAt)}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Cancellation Date</p>
                    <p className="text-sm text-gray-500">{formatDate(cancellation.cancellationDate)}</p>
                  </div>
                </div>
                {cancellation.refundDate && (
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Refund Processed</p>
                      <p className="text-sm text-gray-500">{formatDate(cancellation.refundDate)}</p>
                    </div>
                  </div>
                )}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-gray-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Last Updated</p>
                    <p className="text-sm text-gray-500">{formatDate(cancellation.updatedAt)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Cancellation ${cancellation.cancellationId}`}
      subtitle={`${cancellation.guestName} • ${cancellation.hotelName}`}
      tabs={tabs}
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-3">
          {/* Status Badges */}
          <div className="flex items-center space-x-2">
            {getStatusBadge(cancellation.cancellationStatus, 'cancellation')}
            {getStatusBadge(cancellation.refundStatus, 'refund')}
          </div>
        </div>
      }
    />
  );
}
