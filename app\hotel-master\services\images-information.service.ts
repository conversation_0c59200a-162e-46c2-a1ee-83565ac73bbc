import axiosInstance from "../../api/axiosInstance";

// Image interface - Updated to match API response
export interface HotelImage {
  id: number;
  image_path: string;
  alt_text: string;
  image_category_type: string;
  entity_type: string;
  entity_id: number;
  hotel_id_context: number;
  is_hero_image: boolean;
  image_width: number;
  image_height: number;
  image_size: string;
  image_quality: string;
  image_format: string;
  sort_order: number;
  created_at: string;
  updated_at: string;

  // Legacy fields for backward compatibility
  hotel_id?: string;
  room_id?: string;
  image_url?: string;
  image_type?: 'exterior' | 'interior' | 'room' | 'amenity' | 'dining' | 'recreation' | 'other';
  title?: string;
  description?: string;
  is_primary?: boolean;
  is_active?: boolean;
  file_size?: number;
  dimensions?: string;
}

// Hotel Information interface
export interface HotelInformation {
  id: string;
  hotel_id: string;
  info_type: 'general' | 'location' | 'amenities' | 'dining' | 'recreation' | 'business' | 'accessibility';
  title: string;
  content: string;
  additional_details?: string;
  is_featured: boolean;
  is_active: boolean;
  sort_order: number;
  language_code: string;
  created_at: string;
  updated_at: string;
}

// Request interfaces - Updated to match API structure
export interface CreateImageRequest {
  image_path: string;
  alt_text: string;
  image_category_type: string;
  entity_type: string;
  entity_id: number;
  hotel_id_context: number;
  is_hero_image: boolean;
  image_width: number;
  image_height: number;
  image_size: string;
  image_quality: string;
  image_format: string;
  sort_order: number;

  // Legacy fields for backward compatibility
  hotel_id?: string;
  room_id?: string;
  image_url?: string;
  image_type?: 'exterior' | 'interior' | 'room' | 'amenity' | 'dining' | 'recreation' | 'other';
  title?: string;
  description?: string;
  is_primary?: boolean;
  is_active?: boolean;
  file_size?: number;
  dimensions?: string;
}

export interface CreateHotelInformationRequest {
  hotel_id: string;
  info_type: 'general' | 'location' | 'amenities' | 'dining' | 'recreation' | 'business' | 'accessibility';
  title: string;
  content: string;
  additional_details?: string;
  is_featured: boolean;
  is_active: boolean;
  sort_order: number;
  language_code: string;
}

export interface UpdateImageRequest {
  image_path?: string;
  alt_text?: string;
  image_category_type?: string;
  entity_type?: string;
  entity_id?: number;
  hotel_id_context?: number;
  is_hero_image?: boolean;
  image_width?: number;
  image_height?: number;
  image_size?: string;
  image_quality?: string;
  image_format?: string;
  sort_order?: number;

  // Legacy fields for backward compatibility
  image_url?: string;
  image_type?: 'exterior' | 'interior' | 'room' | 'amenity' | 'dining' | 'recreation' | 'other';
  title?: string;
  description?: string;
  is_primary?: boolean;
  is_active?: boolean;
  file_size?: number;
  dimensions?: string;
}

export interface UpdateHotelInformationRequest {
  info_type?: 'general' | 'location' | 'amenities' | 'dining' | 'recreation' | 'business' | 'accessibility';
  title?: string;
  content?: string;
  additional_details?: string;
  is_featured?: boolean;
  is_active?: boolean;
  sort_order?: number;
  language_code?: string;
}

// Images API
export const createImage = async (imageData: CreateImageRequest, retries = 3): Promise<HotelImage> => {
  try {
    const response = await axiosInstance.post<HotelImage>('/images/', imageData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating image:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createImage(imageData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

export const getHotelImages = async (hotelId: string | number, retries = 3): Promise<HotelImage[]> => {
  try {
    const response = await axiosInstance.get<HotelImage[]>(`/hotels/${hotelId}/images/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel images:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelImages(hotelId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};

export const updateImage = async (imageId: string, imageData: UpdateImageRequest, retries = 3): Promise<HotelImage> => {
  try {
    const response = await axiosInstance.put<HotelImage>(`/images/${imageId}`, imageData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating image:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateImage(imageId, imageData, retries - 1);
    }

    // Throw error if update fails
    throw error;
  }
};

export const deleteImage = async (imageId: string, retries = 3): Promise<void> => {
  try {
    await axiosInstance.delete(`/images/${imageId}`);
  } catch (error: any) {
    console.error('Error deleting image:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return deleteImage(imageId, retries - 1);
    }

    throw error;
  }
};

// Hotel Information API
export const createHotelInformation = async (infoData: CreateHotelInformationRequest, retries = 3): Promise<HotelInformation> => {
  try {
    const response = await axiosInstance.post<HotelInformation>('/hotel-information/', infoData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating hotel information:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotelInformation(infoData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

export const getHotelInformation = async (hotelId: string, retries = 3): Promise<HotelInformation[]> => {
  try {
    const response = await axiosInstance.get<HotelInformation[]>(`/hotels/${hotelId}/information/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel information:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelInformation(hotelId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};


