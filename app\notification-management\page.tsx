"use client";

import { useState } from "react";
import NotificationTemplates from "./components/notification-templates/NotificationTemplates";
import ProviderConfigs from "./components/provider-configs/ProviderConfigs";
import NotificationSettings from "./components/notification-settings/NotificationSettings";

export default function NotificationManagementPage() {
  const [activeTab, setActiveTab] = useState<
    "notification-templates" | "provider-configs" | "notification-settings"
  >("notification-templates");


  const tabs = [
       {
      id: 'notification-templates' as const,
      label: 'Notification Templates',
      icon: 'ri-notification-line',
      description: 'Manage notification templates'
    },
       {
      id: 'provider-configs' as const,
      label: 'Provider Configs',
      icon: 'ri-settings-3-line',
      description: 'Manage Provider configurations'
    },
       {
      id: 'notification-settings' as const,
      label: 'Notification Settings',
      icon: 'ri-equalizer-line',
      description: 'Manage Notification Settings'
    },
  ]

  return (
  <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <i className="ri-building-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                       Notification Management
                  </h1>
                  <p className="text-sm text-gray-600">
                     Manage templates, provider configurations, and notification settings
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700">System Online</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <i className="ri-time-line"></i>
                <span className="whitespace-nowrap">Last sync: 2 minutes ago</span>
              </div>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 rounded-lg font-medium shadow-sm transition-colors whitespace-nowrap">
                <i className="ri-refresh-line mr-2"></i>
                Sync All
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                }}
                className={`relative px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2 whitespace-nowrap min-w-max ${
                  activeTab === tab.id
                    ? 'text-blue-600 border-blue-600 bg-blue-50/50'
                    : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    activeTab === tab.id ? 'bg-blue-100' : 'bg-gray-100'
                  }`}>
                    <i className={`${tab.icon} text-base ${
                      activeTab === tab.id ? 'text-blue-600' : 'text-gray-600'
                    }`}></i>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold">{tab.label}</div>
                    <div className="text-xs font-normal opacity-75 hidden sm:block">{tab.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

        {/* Content Area with Internal Scrolling */}
            <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
              <div className="p-6">
                <div className="max-w-full">
                  {activeTab === 'notification-templates' && (
                    <div className="animate-fade-in">
                      <NotificationTemplates />
                    </div>
                  )}
      
                  {activeTab === 'provider-configs' && (
                    <div className="animate-fade-in">
                      <ProviderConfigs />
                    </div>
                  )}

                  {activeTab === 'notification-settings' && (
                    <div className="animate-fade-in">
                      <NotificationSettings />
                    </div>
                  )}
                </div>
              </div>
            </div>

  </div>);
}
