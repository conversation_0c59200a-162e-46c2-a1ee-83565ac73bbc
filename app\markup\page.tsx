'use client';

import { useState, useEffect } from 'react';
import MarkupMaster from './components/MarkupMaster';

export default function MarkupPage() {
  const [lastSyncTime, setLastSyncTime] = useState('--:--:--');

  useEffect(() => {
    setLastSyncTime(new Date().toLocaleTimeString());
  }, []);

  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Professional Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <i className="ri-price-tag-3-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Markup Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Manage pricing markups and provider configurations
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <i className="ri-refresh-line"></i>
                <span>Last sync: {lastSyncTime}</span>
              </div>
              
              <div className="h-6 w-px bg-gray-300"></div>
              
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <i className="ri-download-line mr-2"></i>
                Export
              </button>

              <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <i className="ri-add-line mr-2"></i>
                Add Markup
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <i className="ri-price-tag-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Total Markups</p>
                  <p className="text-lg font-semibold text-blue-700">24</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <i className="ri-check-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-900">Active</p>
                  <p className="text-lg font-semibold text-green-700">18</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                    <i className="ri-pause-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-yellow-900">Inactive</p>
                  <p className="text-lg font-semibold text-yellow-700">6</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <i className="ri-percent-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Avg Markup</p>
                  <p className="text-lg font-semibold text-blue-700">12.5%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area with Internal Scrolling */}
      <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
        <div className="p-6">
          <div className="max-w-full">
            <div className="animate-fade-in">
              <MarkupMaster />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
