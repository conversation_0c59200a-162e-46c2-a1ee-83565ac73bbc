import axiosInstance from "../../api/axiosInstance";
import { Hotel, CreateHotelRequest, UpdateHotelRequest, HotelListResponse, Pagination } from "../models/hotel.model";

// Get all hotels with pagination and enhanced filtering
export const getAllHotels = async (
  page: number = 1,
  pageSize: number = 10,
  filters?: {
    country_code?: string;
    provider_id?: string;
    provider_hotel_id?: string;
    search?: string;
    status?: string;
  },
  retries = 3
): Promise<HotelListResponse> => {
  try {
    const params = new URLSearchParams();
    // Convert page to skip (API uses skip/limit instead of page/page_size)
    const skip = (page - 1) * pageSize;
    params.append('skip', skip.toString());
    params.append('limit', pageSize.toString());

    // Add enhanced filtering parameters
    if (filters?.country_code) params.append('country_code', filters.country_code);
    if (filters?.provider_id) params.append('provider_id', filters.provider_id);
    if (filters?.provider_hotel_id) params.append('provider_hotel_id', filters.provider_hotel_id);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.status) params.append('status', filters.status);

    const url = `/hotels/?${params.toString()}`;
    console.log('Making API request to:', url);
    console.log('Request parameters:', { page, pageSize, skip, filters });

    const response = await axiosInstance.get<HotelListResponse>(url);
    console.log('API response received:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotels:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getAllHotels(page, pageSize, filters, retries - 1);
    }

    // Return empty data on error
    return {
      items: [],
      pagination: {
        total_items: 0,
        total_pages: 0,
        current_page: page,
        page_size: pageSize,
        has_next: false,
        has_previous: false,
        next_page: null,
        previous_page: null
      }
    };
  }
};

// Get all hotels (simple version for backward compatibility)
export const getAllHotelsSimple = async (filters?: any, retries = 3): Promise<Hotel[]> => {
  try {
    const response = await getAllHotels(1, 1000, filters, retries); // Get first 1000 hotels
    return response.items;
  } catch (error: any) {
    console.error('Error fetching hotels:', error);
    return [];
  }
};

// Get hotel by ID
export const getHotelById = async (id: string | number, retries = 3): Promise<Hotel> => {
  try {
    const response = await axiosInstance.get<Hotel>(`/hotels/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel by ID:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelById(id, retries - 1);
    }

    // Throw error if hotel not found
    throw new Error(`Hotel with ID ${id} not found`);
  }
};

// Create new hotel
export const createHotel = async (hotelData: CreateHotelRequest, retries = 3): Promise<Hotel> => {
  try {
    const response = await axiosInstance.post<Hotel>('/hotels/', hotelData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating hotel:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotel(hotelData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

// Update hotel
export const updateHotel = async (id: string, hotelData: UpdateHotelRequest, retries = 3): Promise<Hotel> => {
  try {
    const response = await axiosInstance.put<Hotel>(`/hotels/${id}`, hotelData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating hotel:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateHotel(id, hotelData, retries - 1);
    }

    // Throw error if update fails
    throw error;
  }
};

// Delete hotel
export const deleteHotel = async (id: string, retries = 3): Promise<void> => {
  try {
    await axiosInstance.delete(`/hotels/${id}`);
  } catch (error: any) {
    console.error('Error deleting hotel:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return deleteHotel(id, retries - 1);
    }

    throw error;
  }
};

// Update hotel status
export const updateHotelStatus = async (id: string, status: Hotel['status'], retries = 3): Promise<Hotel> => {
  try {
    const response = await axiosInstance.patch<Hotel>(`/hotels/${id}/status/`, { status });
    return response.data;
  } catch (error: any) {
    console.error('Error updating hotel status:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateHotelStatus(id, status, retries - 1);
    }

    // Throw error if status update fails
    throw error;
  }
};

// Search hotels with pagination - now uses the main hotels endpoint with filters
export const searchHotels = async (
  query: string,
  filters?: any,
  page: number = 1,
  pageSize: number = 10,
  retries = 3
): Promise<HotelListResponse> => {
  // Use the main getAllHotels function with search and filters
  const searchFilters = {
    search: query,
    ...filters
  };

  return getAllHotels(page, pageSize, searchFilters, retries);
};



