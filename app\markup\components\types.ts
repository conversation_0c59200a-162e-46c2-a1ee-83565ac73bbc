export interface Markup {
  id: string;
  name: string;
  providerType: 'common markup' | 'provider' | 'hotel';
  status: 'active' | 'inactive';
  type: 'percentage' | 'rate';
  value: number; // percentage value or rate amount
  currency?: string; // for rate type
  description?: string;
  applicableRegions?: string[];
  applicableHotels?: string[];
  applicableProviders?: string[];
  validFrom: string;
  validTo?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  lastModifiedBy?: string;
  priority?: number; // for markup precedence
  conditions?: MarkupCondition[];
}

export interface MarkupCondition {
  id: string;
  type: 'booking_amount' | 'stay_duration' | 'advance_booking' | 'season';
  operator: 'greater_than' | 'less_than' | 'equal_to' | 'between' | 'in';
  value: string | number;
  secondaryValue?: string | number; // for 'between' operator
}

export type ProviderType = 'common markup' | 'provider' | 'hotel';
export type MarkupStatus = 'active' | 'inactive';
export type MarkupType = 'percentage' | 'rate';
