import axiosInstance from "../../api/axiosInstance";

// Facility Type interface - Updated to match API response
export interface FacilityType {
  id: number;
  hotel_id: number;
  name: string;
  created_at: string;
  updated_at: string | null;

  // Optional fields for backward compatibility
  category?: 'amenity' | 'service' | 'recreation' | 'business' | 'accessibility';
  description?: string;
  icon?: string;
  is_active?: boolean;
}

// Facility Detail interface
export interface FacilityDetail {
  id: string;
  facility_type_id: string;
  name: string;
  description?: string;
  additional_info?: string;
  is_chargeable: boolean;
  charge_amount?: number;
  charge_currency?: string;
  availability_hours?: string;
  location?: string;
  capacity?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}



// Request interfaces - Updated to match API structure
export interface CreateFacilityTypeRequest {
  hotel_id: number;
  name: string;

  // Optional fields for enhanced functionality
  category?: 'amenity' | 'service' | 'recreation' | 'business' | 'accessibility';
  description?: string;
  icon?: string;
  is_active?: boolean;
}

export interface CreateFacilityDetailRequest {
  facility_type_id: string;
  name: string;
  description?: string;
  additional_info?: string;
  is_chargeable: boolean;
  charge_amount?: number;
  charge_currency?: string;
  availability_hours?: string;
  location?: string;
  capacity?: number;
  is_active: boolean;
}



// Create hotel facility type
export const createHotelFacilityType = async (facilityData: CreateFacilityTypeRequest, retries = 3): Promise<FacilityType> => {
  try {
    const response = await axiosInstance.post<FacilityType>('/hotel-facility-types/', facilityData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating hotel facility type:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotelFacilityType(facilityData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

// Get facility types by hotel
export const getFacilityTypesByHotel = async (hotelId: string | number, retries = 3): Promise<FacilityType[]> => {
  try {
    const response = await axiosInstance.get<FacilityType[]>(`/hotels/${hotelId}/facility-types/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel facility types:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getFacilityTypesByHotel(hotelId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};

// Create facility detail
export const createFacilityDetail = async (detailData: CreateFacilityDetailRequest, retries = 3): Promise<FacilityDetail> => {
  try {
    const response = await axiosInstance.post<FacilityDetail>('/facility-details/', detailData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating facility detail:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createFacilityDetail(detailData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

// Get facility details by type
export const getFacilityDetailsByType = async (typeId: string, retries = 3): Promise<FacilityDetail[]> => {
  try {
    const response = await axiosInstance.get<FacilityDetail[]>(`/facility-types/${typeId}/details/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching facility details:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getFacilityDetailsByType(typeId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};






