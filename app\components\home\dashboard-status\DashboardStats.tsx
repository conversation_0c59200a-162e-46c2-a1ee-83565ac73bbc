
'use client';

import React from 'react';

type ColorKey = 'blue' | 'emerald' | 'purple' | 'amber' | 'cyan' | 'rose';
type ChangeType = 'positive' | 'negative';

interface StatItem {
  title: string;
  value: string;
  change: string;
  changeType: ChangeType;
  icon: string;
  color: ColorKey;
  trend: string;
  subtitle: string;
  additionalInfo: string;
}

export default function DashboardStats() {
  const stats: StatItem[] = [
    {
      title: 'Total Bookings',
      value: '47,892',
      change: '+18.2%',
      changeType: 'positive',
      icon: 'ri-calendar-check-fill',
      color: 'blue',
      trend: 'up',
      subtitle: 'This month vs last month',
      additionalInfo: '2,847 today'
    },
    {
      title: 'Gross Revenue',
      value: '$8.7M',
      change: '+24.5%',
      changeType: 'positive',
      icon: 'ri-money-dollar-circle-fill',
      color: 'emerald',
      trend: 'up',
      subtitle: 'Monthly recurring revenue',
      additionalInfo: '$289K today'
    },
    {
      title: 'Active Properties',
      value: '2,847',
      change: '+12.3%',
      changeType: 'positive',
      icon: 'ri-building-2-fill',
      color: 'purple',
      trend: 'up',
      subtitle: 'Verified hotel partners',
      additionalInfo: '47 new this week'
    },
    {
      title: 'Customer Satisfaction',
      value: '4.89',
      change: '+0.12',
      changeType: 'positive',
      icon: 'ri-star-fill',
      color: 'amber',
      trend: 'up',
      subtitle: 'Average rating score',
      additionalInfo: '12,847 reviews'
    },
    {
      title: 'Support Resolution',
      value: '97.2%',
      change: '****%',
      changeType: 'positive',
      icon: 'ri-customer-service-2-fill',
      color: 'cyan',
      trend: 'up',
      subtitle: 'First contact resolution',
      additionalInfo: 'Avg 4.2 min response'
    },
    {
      title: 'Conversion Rate',
      value: '12.8%',
      change: '****%',
      changeType: 'positive',
      icon: 'ri-arrow-up-circle-fill',
      color: 'rose',
      trend: 'up',
      subtitle: 'Visitor to booking ratio',
      additionalInfo: 'Above industry avg'
    }
  ];

  const colorClasses: Record<ColorKey, string> = {
    blue: 'from-blue-500 to-blue-600',
    emerald: 'from-emerald-500 to-emerald-600',
    purple: 'from-purple-500 to-purple-600',
    amber: 'from-amber-500 to-amber-600',
    cyan: 'from-cyan-500 to-cyan-600',
    rose: 'from-rose-500 to-rose-600'
  };

  const backgroundColors: Record<ColorKey, string> = {
    blue: 'bg-blue-50 border-blue-100',
    emerald: 'bg-emerald-50 border-emerald-100',
    purple: 'bg-purple-50 border-purple-100',
    amber: 'bg-amber-50 border-amber-100',
    cyan: 'bg-cyan-50 border-cyan-100',
    rose: 'bg-rose-50 border-rose-100'
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {stats.map((stat, index) => (
        <div key={index} className={`bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow ${backgroundColors[stat.color]} overflow-hidden`}>
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <div className={`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ${colorClasses[stat.color]} rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0`}>
              <i className={`${stat.icon} text-white text-lg sm:text-xl`}></i>
            </div>
            <div className="text-right ml-2">
              <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${
                stat.changeType === 'positive' ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
              } whitespace-nowrap`}>
                <i className={`${stat.changeType === 'positive' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'} mr-1`}></i>
                {stat.change}
              </div>
            </div>
          </div>

          <div className="mb-3 min-w-0">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate">{stat.value}</h3>
            <p className="text-sm font-medium text-slate-600 truncate">{stat.title}</p>
          </div>

          <div className="space-y-1 min-w-0">
            <p className="text-xs text-slate-500 truncate">{stat.subtitle}</p>
            <p className="text-xs text-slate-400 truncate">{stat.additionalInfo}</p>
          </div>

          <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-slate-200">
            <div className="flex items-center justify-between">
              <span className="text-xs text-slate-500">Trend</span>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <div className="w-2 h-2 bg-emerald-300 rounded-full"></div>
                <div className="w-2 h-2 bg-slate-200 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}