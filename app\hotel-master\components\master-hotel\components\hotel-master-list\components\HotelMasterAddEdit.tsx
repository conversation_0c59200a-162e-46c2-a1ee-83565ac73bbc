'use client';

import { useState, useEffect, FormEvent } from 'react';
import { hotelDetail, Policy } from '@/app/hotel-master/hotel-master.model';
import { SearchableSelect } from '@/app/components/search-select-input/SearchSelectInput';
import Image from 'next/image';
import TabbedModal, { TabDefinition } from '@/app/components/ui/ModalWithTabs';

interface HotelAddEditProps {
  isOpen: boolean;
  hotel?: hotelDetail | null;
  onSave: (hotelData: Partial<hotelDetail>) => void;
  onClose: () => void;
  mode?: 'add' | 'edit';
}

const tabs: TabDefinition[] = [
  { id: 'basic', label: 'Basic Info', icon: 'ri-information-line' },
  { id: 'location', label: 'Location', icon: 'ri-map-pin-line' },
  { id: 'provider', label: 'Provider Info', icon: 'ri-building-line' },
  { id: 'facilities', label: 'Amenities', icon: 'ri-service-line' },
  { id: 'policies', label: 'Policies', icon: 'ri-file-text-line' },
  { id: 'gallery', label: 'Gallery', icon: 'ri-image-line' }
];

export default function MasterHotelAddEdit({ isOpen, hotel, onSave, onClose, mode = 'add' }: HotelAddEditProps) {
  const [formData, setFormData] = useState<Partial<hotelDetail>>({ name: '', city: '', country: '', HotelType: '', starRating: '1', provider_name: '', hotel_id: '', address: '', about: '', phones_json: [''], faxes_json: [''], chain_name: '', provider_id: '', provider_hotel_id: '', geoLocationInfo: { lat: 0, lon: 0 }, amenities: [], policies: [], images: [] });
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (hotel) {
        setFormData({ ...hotel, phones_json: hotel.phones_json || [''], faxes_json: hotel.faxes_json || [''] });
      } else {
        setFormData({ name: '', city: '', country: '', HotelType: '', starRating: '1', provider_name: '', hotel_id: '', address: '', about: '', phones_json: [''], faxes_json: [''], chain_name: '', provider_id: '', provider_hotel_id: '', geoLocationInfo: { lat: 0, lon: 0 }, amenities: [], policies: [], images: [] });
        setImagePreviewUrls([]);
        setUploadedImages([]);
        setErrors({});
      }
    }
  }, [hotel, isOpen]);

  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (formData.name && formData.country && formData.city) {
        setIsAutoSaving(true);
        setTimeout(() => setIsAutoSaving(false), 1000);
      }
    }, 3000);
    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  const handleInputChange = (field: keyof Partial<hotelDetail>, value: string | number | boolean | File[]) => { setFormData(prev => ({ ...prev, [field]: value })); if (errors[field]) { setErrors(prev => ({ ...prev, [field]: '' })); } };
  const handleNestedInputChange = (parent: keyof Partial<hotelDetail>, field: 'lat' | 'lon', value: number) => { setFormData(prev => { const parentObject = prev[parent] as { lat: number, lon: number } | undefined; return { ...prev, [parent]: { ...parentObject, [field]: value } }; }); };
  const handleArrayChange = (field: keyof Partial<hotelDetail>, index: number, value: string) => { setFormData(prev => { const newArray = [...(prev[field] as string[])]; newArray[index] = value; return { ...prev, [field]: newArray }; }); };
  const handleAddArrayItem = (field: keyof Partial<hotelDetail>) => { setFormData(prev => ({ ...prev, [field]: [...(prev[field] as string[]), ''] })); };
  const handleRemoveArrayItem = (field: keyof Partial<hotelDetail>, index: number) => { setFormData(prev => ({ ...prev, [field]: (prev[field] as any[]).filter((_, i) => i !== index) })); };
  const handlePolicyChange = (field: string, value: string) => { setFormData(prev => { const newPolicies = prev.policies ? [...prev.policies] : []; const existingPolicyIndex = newPolicies.findIndex(p => p.name === field); if (existingPolicyIndex > -1) { newPolicies[existingPolicyIndex] = { ...newPolicies[existingPolicyIndex], description: value }; } else { newPolicies.push({ id: Math.random(), name: field, description: value } as Policy); } return { ...prev, policies: newPolicies }; }); };
  const handleFacilityToggle = (facility: string) => { setFormData(prev => { const newAmenities = prev.amenities ? [...prev.amenities] : []; const isSelected = newAmenities.includes(facility); return { ...prev, amenities: isSelected ? newAmenities.filter(f => f !== facility) : [...newAmenities, facility] }; }); };
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => { const files = event.target.files; if (files) { const newFiles = Array.from(files); setUploadedImages(prev => [...prev, ...newFiles]); newFiles.forEach(file => { const reader = new FileReader(); reader.onload = (e) => { if (e.target?.result) { setImagePreviewUrls(prev => [...prev, e.target!.result as string]); } }; reader.readAsDataURL(file); }); } };
  const handleImageRemove = (index: number) => { setUploadedImages(prev => prev.filter((_, i) => i !== index)); setImagePreviewUrls(prev => prev.filter((_, i) => i !== index)); };
  const validateForm = () => { const newErrors: Record<string, string> = {}; if (!formData.name?.trim()) newErrors.name = 'Hotel name is required'; if (!formData.HotelType?.trim()) newErrors.HotelType = 'Hotel type is required'; if (!formData.country?.trim()) newErrors.country = 'Country is required'; if (!formData.city?.trim()) newErrors.city = 'City is required'; if (!formData.address?.trim()) newErrors.address = 'Address is required'; if (!formData.phones_json?.[0]?.trim()) newErrors.phone = 'At least one phone number is required'; setErrors(newErrors); return Object.keys(newErrors).length === 0; };
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => { e.preventDefault(); if (!validateForm()) return; setIsSubmitting(true); try { const dataToSave = hotel ? { ...formData, id: hotel.id } : formData; await onSave(dataToSave as hotelDetail); } catch (error) { console.error('Error saving hotel:', error); } finally { setIsSubmitting(false); } };

  const isEditMode = mode === 'edit' && hotel;
  const formTitle = isEditMode ? `Edit Hotel: ${hotel.name}` : 'Add New Hotel';
  const formSubtitle = isEditMode ? 'Update hotel information and settings' : 'Create a new hotel with all necessary details';
  const submitButtonText = isEditMode ? 'Update Hotel' : 'Create Hotel';
  
  const facilityOptions = ['Free WiFi', 'Swimming Pool', 'Spa & Wellness', 'Fitness Center', 'Restaurant', 'Room Service', 'Bar/Lounge', 'Business Center', 'Conference Rooms', 'Parking', 'Pet Friendly', 'Airport Shuttle', 'Concierge Service', 'Laundry Service', 'Air Conditioning', 'Heating', 'Safe', 'Minibar', 'Balcony/Terrace'];
  const hotelTypes = ['Luxury Resort', 'Business Hotel', 'Boutique Hotel', 'Beach Resort', 'Mountain Lodge', 'City Hotel', 'Airport Hotel', 'Spa Hotel', 'Extended Stay', 'Hostel', 'Motel', 'Apartment Hotel'];
  const commonCountryCodes = [{ id: 'AE', name: 'United Arab Emirates' }, { id: 'US', name: 'United States' }];
  const commonCities = [{ id: 'Dubai', name: 'Dubai' }, { id: 'New York', name: 'New York' }];
  
  const inputStyle = "w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder:text-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent";
  const errorInputStyle = "border-red-300 bg-red-50";

  const headerActions = (
    <>
      {isAutoSaving && (<div className="flex items-center text-sm text-green-600"><i className="ri-save-line mr-1"></i>Auto-saved</div>)}
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isEditMode ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>{isEditMode ? 'Edit Mode' : 'Add Mode'}</span>
    </>
  );

  const modalFooter = (
    <div className="flex justify-end space-x-4">
      <button type="button" onClick={onClose} className="px-6 py-2 text-gray-600 bg-white rounded-lg border border-gray-300 hover:bg-gray-50">Cancel</button>
      <button type="submit" form="hotel-form" disabled={isSubmitting} className={`px-6 py-2 text-white rounded-lg font-medium ${isSubmitting ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}>
        {isSubmitting ? <i className="ri-loader-line animate-spin mr-2"></i> : <i className="ri-save-line mr-2"></i>}
        {submitButtonText}
      </button>
    </div>
  );

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={formTitle}
      subtitle={formSubtitle}
      headerActions={headerActions}
      tabs={tabs}
      footer={modalFooter}
      size="xl"
      formId="hotel-form"
      onSubmit={handleSubmit}
    >
      <TabbedModal.Content tabId="basic">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Hotel Name *</label>
              <input type="text" value={formData.name || ''} onChange={(e) => handleInputChange('name', e.target.value)} className={`${inputStyle} ${errors.name ? errorInputStyle : ''}`} required />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Hotel ID</label>
              <input type="text" value={formData.hotel_id || ''} onChange={(e) => handleInputChange('hotel_id', e.target.value)} className={inputStyle} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Hotel Type *</label>
              <select value={formData.HotelType || ''} onChange={(e) => handleInputChange('HotelType', e.target.value)} className={`${inputStyle} ${errors.HotelType ? errorInputStyle : ''}`} required>
                <option value="">Select hotel type</option>
                {hotelTypes.map((type) => (<option key={type} value={type}>{type}</option>))}
              </select>
              {errors.HotelType && <p className="mt-1 text-sm text-red-600">{errors.HotelType}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Star Rating</label>
              <select value={formData.starRating || '1'} onChange={(e) => handleInputChange('starRating', e.target.value)} className={inputStyle}>
                {[1, 2, 3, 4, 5].map((rating) => (<option key={rating} value={rating}>{rating} Star{rating > 1 ? 's' : ''}</option>))}
              </select>
            </div>
          </div>
        </div>
      </TabbedModal.Content>
      <TabbedModal.Content tabId="location">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SearchableSelect placeholder='Search countries...' label="Country *" options={commonCountryCodes} value={formData.country || ''} onChange={(value) => handleInputChange('country', value)} />
            <SearchableSelect placeholder='Search cities...' label="City *" options={commonCities} value={formData.city || ''} onChange={(value) => handleInputChange('city', value)} />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Address *</label>
            <textarea value={formData.address || ''} onChange={(e) => handleInputChange('address', e.target.value)} rows={3} className={`${inputStyle} ${errors.address ? errorInputStyle : ''}`} required />
            {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
              <input type="number" step="any" value={formData.geoLocationInfo?.lat || 0} onChange={(e) => handleNestedInputChange('geoLocationInfo', 'lat', parseFloat(e.target.value) || 0)} className={inputStyle} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
              <input type="number" step="any" value={formData.geoLocationInfo?.lon || 0} onChange={(e) => handleNestedInputChange('geoLocationInfo', 'lon', parseFloat(e.target.value) || 0)} className={inputStyle} />
            </div>
          </div>
        </div>
      </TabbedModal.Content>
      <TabbedModal.Content tabId="provider">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div><label className="block text-sm font-medium text-gray-700 mb-2">Provider ID</label><input type="text" value={formData.provider_id || ''} onChange={(e) => handleInputChange('provider_id', e.target.value)} className={inputStyle} /></div>
          <div><label className="block text-sm font-medium text-gray-700 mb-2">Provider Name</label><input type="text" value={formData.provider_name || ''} onChange={(e) => handleInputChange('provider_name', e.target.value)} className={inputStyle} /></div>
          <div><label className="block text-sm font-medium text-gray-700 mb-2">Provider Hotel ID</label><input type="text" value={formData.provider_hotel_id || ''} onChange={(e) => handleInputChange('provider_hotel_id', e.target.value)} className={inputStyle} /></div>
          <div><label className="block text-sm font-medium text-gray-700 mb-2">Chain Name</label><input type="text" value={formData.chain_name || ''} onChange={(e) => handleInputChange('chain_name', e.target.value)} className={inputStyle} /></div>
        </div>
      </TabbedModal.Content>
      <TabbedModal.Content tabId="facilities">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {facilityOptions.map((facility) => (<label key={facility} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"><input type="checkbox" checked={formData.amenities?.includes(facility) || false} onChange={() => handleFacilityToggle(facility)} className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" /><span className="text-sm font-medium text-gray-700">{facility}</span></label>))}
        </div>
      </TabbedModal.Content>
      <TabbedModal.Content tabId="policies">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
              <input type="time" value={formData.policies?.find(p => p.name === 'check_in')?.description || '15:00'} onChange={(e) => handlePolicyChange('check_in', e.target.value)} className={inputStyle} />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
              <input type="time" value={formData.policies?.find(p => p.name === 'check_out')?.description || '11:00'} onChange={(e) => handlePolicyChange('check_out', e.target.value)} className={inputStyle} />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Cancellation Policy</label>
            <textarea value={formData.policies?.find(p => p.name === 'cancellation')?.description || ''} onChange={(e) => handlePolicyChange('cancellation', e.target.value)} rows={4} className={inputStyle} />
          </div>
        </div>
      </TabbedModal.Content>
      <TabbedModal.Content tabId="gallery">
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Hotel Images</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <input type="file" multiple accept="image/*" onChange={handleImageUpload} className="hidden" id="image-upload" />
              <label htmlFor="image-upload" className="cursor-pointer"><i className="ri-upload-cloud-2-line text-4xl text-gray-400 mb-2 block"></i><p className="text-sm text-gray-600">Click to upload or drag and drop</p></label>
            </div>
          </div>
          {(imagePreviewUrls.length > 0 || (formData.images && formData.images.length > 0)) && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Gallery Preview</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {formData.images?.map((image, index) => (<div key={image.id || index} className="relative group aspect-w-1 aspect-h-1"><Image src={image.image_path} alt={image.alt_text || ''} layout="fill" objectFit="cover" className="rounded-lg" unoptimized /><button type="button" onClick={() => handleRemoveArrayItem('images', index)} className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100">&times;</button></div>))}
                {imagePreviewUrls.map((url, index) => (<div key={url} className="relative group aspect-w-1 aspect-h-1"><Image src={url} alt="" layout="fill" objectFit="cover" className="rounded-lg" unoptimized /><button type="button" onClick={() => handleImageRemove(index)} className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100">&times;</button></div>))}
              </div>
            </div>
          )}
        </div>
      </TabbedModal.Content>
    </TabbedModal>
  );
}