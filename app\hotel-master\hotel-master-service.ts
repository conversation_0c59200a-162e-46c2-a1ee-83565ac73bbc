import apiService from "../api/api-service"
import { hotelDetail, HotelListApiResponse } from "./hotel-master.model"

export const getHotelListApi = async (skip: number, limit: number, filters: any) : Promise<HotelListApiResponse> => {
    return apiService.get<HotelListApiResponse>(`/hotels?skip=${skip}&limit=${limit}`)
}

export const getHotelDetailsApi = async (id: number) : Promise<hotelDetail> => {
    return apiService.get<hotelDetail>(`/hotels/${id}/details`)
}

export const editHotelApi = async (id: string, hotel: hotelDetail) : Promise<hotelDetail> => {
    return apiService.put<hotelDetail>(`/hotels/${id}`, hotel)
}