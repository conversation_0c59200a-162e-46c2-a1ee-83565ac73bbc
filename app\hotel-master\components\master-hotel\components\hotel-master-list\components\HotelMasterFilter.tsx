import { SearchableSelect } from '@/app/components/search-select-input/SearchSelectInput';
import React, { useState } from 'react';

export interface hotelMasterFilterData {
    search: string;
    status: string;
    country: string;
    starRating: string;
    contractType: string;
    provider: string;
    country_code: string;
    provider_id: string;
    provider_hotel_id: string;
    has_phone: boolean;
    has_attributes: boolean;
}

interface HotelMasterFilterProps {
    filters: hotelMasterFilterData;
    onFiltersChange: (filters: hotelMasterFilterData) => void;
    onApplyFilters: () => void;
    onClearFilters: () => void;
}

function HotelMasterFilter({ filters, onFiltersChange, onClearFilters, onApplyFilters }: HotelMasterFilterProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    const handleFilterChange = (key: keyof hotelMasterFilterData, value: string | boolean) => {
        onFiltersChange({
            ...filters,
            [key]: value,
        });
    };

    const commonCountryCodes = [
        { id: 'AE', name: 'United Arab Emirates' },
        { id: 'US', name: 'United States' },
        { id: 'GB', name: 'United Kingdom' },
        { id: 'FR', name: 'France' },
        { id: 'DE', name: 'Germany' },
        { id: 'IT', name: 'Italy' },
        { id: 'ES', name: 'Spain' },
        { id: 'TH', name: 'Thailand' },
        { id: 'SG', name: 'Singapore' },
        { id: 'JP', name: 'Japan' },
    ];

    const commonProviders = [
        { id: 'EAN', name: 'Expedia (EAN)' },
        { id: 'BDC', name: 'Booking.com' },
        { id: 'HRS', name: 'HRS' },
        { id: 'AGODA', name: 'Agoda' },
        { id: 'HOTELBEDS', name: 'Hotelbeds' },
    ];
    
    // Consistent input/select styling classes
    const inputBaseClasses = "w-full h-11 px-4 py-2 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors duration-200 cursor-pointer";
    const labelClasses = "block text-sm font-medium text-gray-700 mb-2";

    const statusOptions = [
        { id: '', name: 'All Status' },
        { id: 'active', name: 'Active' },
        { id: 'inactive', name: 'Inactive' },
        { id: 'pending', name: 'Pending' },
    ];

    const starRatingOptions = [
        { id: '', name: 'All Ratings' },
        { id: '5', name: '5 Stars' },
        { id: '4', name: '4 Stars' },
        { id: '3', name: '3 Stars' },
        { id: '2', name: '2 Stars' },
        { id: '1', name: '1 Star' },
    ];

    const contractTypeOptions = [
        { id: '', name: 'All Types' },
        { id: 'static', name: 'Static' },
        { id: 'dynamic', name: 'Dynamic' },
    ];

    return (
        <div className="w-full bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Enhanced Filters</h3>
                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 cursor-pointer"
                >
                    <span>{isExpanded ? 'Show Less' : 'Show More'}</span>
                    <svg
                        className={`w-4 h-4 transition-transform duration-300 ease-in-out ${isExpanded ? 'rotate-180' : 'rotate-0'}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>

            {/* Basic Filters - Always Visible */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div className="space-y-2 relative z-20">
                    <label className={labelClasses}>
                        Search Hotels
                    </label>
                    <input
                        type="text"
                        value={filters.search}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                        placeholder="Hotel name, city, or address..."
                        className={inputBaseClasses}
                    />
                </div>

                <div className="space-y-2 relative z-20">
                    <SearchableSelect
                        label="Status"
                        options={statusOptions}
                        value={filters.status}
                        onChange={(value: string) => handleFilterChange('status', value)}
                        placeholder="All Status"
                        className={inputBaseClasses}
                    />
                </div>

                <div className="space-y-2 relative z-20">
                    <SearchableSelect
                        label="Country"
                        options={commonCountryCodes}
                        value={filters.country_code}
                        onChange={(value: string) => handleFilterChange('country_code', value)}
                        placeholder="Search countries..."
                        className={inputBaseClasses}
                    />
                </div>

                <div className="space-y-2 relative z-20">
                    <SearchableSelect
                        label="Provider"
                        options={commonProviders}
                        value={filters.provider_id}
                        onChange={(value: string) => handleFilterChange('provider_id', value)}
                        placeholder="Search providers..."
                        className={inputBaseClasses}
                    />
                </div>
            </div>

            {/* Advanced Filters - Expandable */}
            <div className={`grid transition-[grid-template-rows] duration-500 ease-in-out ${
                isExpanded ? 'grid-rows-[1fr]' : 'grid-rows-[0fr] pointer-events-none'
            }`}>
                <div className={`min-h-0 overflow-visible transition-opacity duration-500 ease-in-out ${isExpanded ? 'opacity-100' : 'opacity-0'}`}>
                    <div className="border-t border-gray-200 pt-6 mt-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                            <div className="space-y-2">
                                <label className={labelClasses}>
                                    Provider Hotel ID
                                </label>
                                <input
                                    type="text"
                                    value={filters.provider_hotel_id || ''}
                                    onChange={(e) => handleFilterChange('provider_hotel_id', e.target.value)}
                                    placeholder="e.g., 16216917"
                                    className={inputBaseClasses}
                                />
                            </div>

                            <div className="space-y-2 relative z-10">
                                <SearchableSelect
                                    label="Star Rating"
                                    options={starRatingOptions}
                                    value={filters.starRating}
                                    onChange={(value: string) => handleFilterChange('starRating', value)}
                                    placeholder="All Ratings"
                                    className={inputBaseClasses}
                                />
                            </div>

                            <div className="space-y-2 relative z-10">
                                <SearchableSelect
                                    label="Contract Type"
                                    options={contractTypeOptions}
                                    value={filters.contractType}
                                    onChange={(value: string) => handleFilterChange('contractType', value)}
                                    placeholder="All Types"
                                    className={inputBaseClasses}
                                />
                            </div>
                        </div>

                        {/* Special Filters */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div className="flex items-start space-x-3">
                                <div className="flex items-center h-5">
                                    <input
                                        id="has_phone"
                                        type="checkbox"
                                        checked={filters.has_phone || false}
                                        onChange={(e) => handleFilterChange('has_phone', e.target.checked)}
                                        className="h-4 w-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                                    />
                                </div>
                                <div className="text-sm leading-5">
                                    <label htmlFor="has_phone" className="font-medium text-gray-700 cursor-pointer">
                                        Has Phone Number
                                    </label>
                                    <p className="text-gray-500">Filter hotels with phone numbers</p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <div className="flex items-center h-5">
                                    <input
                                        id="has_attributes"
                                        type="checkbox"
                                        checked={filters.has_attributes || false}
                                        onChange={(e) => handleFilterChange('has_attributes', e.target.checked)}
                                        className="h-4 w-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                                    />
                                </div>
                                <div className="text-sm leading-5">
                                    <label htmlFor="has_attributes" className="font-medium text-gray-700 cursor-pointer">
                                        Has Attributes
                                    </label>
                                    <p className="text-gray-500">Filter hotels with additional attributes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                    Enhanced filtering with country, provider, and attribute support
                </div>
                <div className="flex items-center space-x-3">
                    <button
                        onClick={onClearFilters}
                        className="inline-flex items-center px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer"
                    >
                        Clear All
                    </button>
                    <button
                        onClick={onApplyFilters}
                        className="inline-flex items-center px-6 py-2.5 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
                    >
                        Apply Filters
                    </button>
                </div>
            </div>
        </div>
    );
}

export default HotelMasterFilter;
