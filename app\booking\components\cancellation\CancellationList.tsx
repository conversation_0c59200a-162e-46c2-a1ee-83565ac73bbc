'use client';

import { useState, useEffect } from 'react';
import { Cancellation, CancellationStatus, RefundStatus, getCancellationStatusText, getRefundStatusText } from '../types';
import { usePagination } from '../../../hooks/usePagination';
import Pagination from '../../../styles/components/Pagination';

interface CancellationListProps {
  cancellations: Cancellation[];
  onView: (cancellation: Cancellation) => void;
}

export default function CancellationList({ cancellations, onView }: CancellationListProps) {
  const [sortField, setSortField] = useState<keyof Cancellation>('cancellationDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Initialize pagination
  const pagination = usePagination<Cancellation>({ initialItemsPerPage: 5 });

  // Update pagination data when cancellations change
  useEffect(() => {
    const sortedCancellations = [...cancellations].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    pagination.setPaginationData(sortedCancellations);
  }, [cancellations, sortField, sortDirection]);

  const handleSort = (field: keyof Cancellation) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getCancellationStatusBadge = (status: CancellationStatus) => {
    const statusConfig = {
      'pending': 'bg-amber-100 text-amber-800 border-amber-200',
      'approved': 'bg-blue-100 text-blue-800 border-blue-200',
      'rejected': 'bg-red-100 text-red-800 border-red-200',
      'processed': 'bg-green-100 text-green-800 border-green-200',
      'completed': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'cancelled': 'bg-gray-100 text-gray-800 border-gray-200'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig[status]}`}>
        {getCancellationStatusText(status)}
      </span>
    );
  };

  const getRefundStatusBadge = (status: RefundStatus) => {
    const statusConfig = {
      'not-applicable': 'bg-gray-100 text-gray-800 border-gray-200',
      'pending': 'bg-amber-100 text-amber-800 border-amber-200',
      'processing': 'bg-blue-100 text-blue-800 border-blue-200',
      'completed': 'bg-green-100 text-green-800 border-green-200',
      'failed': 'bg-red-100 text-red-800 border-red-200',
      'partial': 'bg-orange-100 text-orange-800 border-orange-200',
      'rejected': 'bg-red-100 text-red-800 border-red-200'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig[status]}`}>
        {getRefundStatusText(status)}
      </span>
    );
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (cancellations.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <i className="ri-close-circle-line text-2xl text-gray-400"></i>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No cancellations found</h3>
        <p className="text-gray-500">There are no cancellations matching your current filters.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('cancellationId')}
              >
                <div className="flex items-center">
                  Cancellation ID
                  {sortField === 'cancellationId' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('guestName')}
              >
                <div className="flex items-center">
                  Guest Details
                  {sortField === 'guestName' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('hotelName')}
              >
                <div className="flex items-center">
                  Hotel & Room
                  {sortField === 'hotelName' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('cancellationDate')}
              >
                <div className="flex items-center">
                  Cancellation Date
                  {sortField === 'cancellationDate' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('refundAmount')}
              >
                <div className="flex items-center">
                  Refund Amount
                  {sortField === 'refundAmount' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('cancellationStatus')}
              >
                <div className="flex items-center">
                  Status
                  {sortField === 'cancellationStatus' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('refundStatus')}
              >
                <div className="flex items-center">
                  Refund Status
                  {sortField === 'refundStatus' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {pagination.paginatedData.map((cancellation) => (
              <tr key={cancellation.id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-semibold text-gray-900">{cancellation.cancellationId}</div>
                  <div className="text-sm text-gray-500">Booking: {cancellation.bookingReference}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {cancellation.guestName.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{cancellation.guestName}</div>
                      <div className="text-sm text-gray-500">{cancellation.guestEmail}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{cancellation.hotelName}</div>
                  <div className="text-sm text-gray-500">{cancellation.roomName}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{formatDate(cancellation.cancellationDate)}</div>
                  <div className="text-sm text-gray-500">{cancellation.numberOfNights} nights</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(cancellation.refundAmount, cancellation.currency)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Fee: {formatCurrency(cancellation.cancellationFee, cancellation.currency)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getCancellationStatusBadge(cancellation.cancellationStatus)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getRefundStatusBadge(cancellation.refundStatus)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end">
                    <button
                      onClick={() => onView(cancellation)}
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <i className="ri-eye-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {cancellations.length > 0 && (
        <div className="mt-6">
          <Pagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            itemsPerPage={pagination.itemsPerPage}
            totalItems={pagination.totalItems}
            onPageChange={pagination.setCurrentPage}
            onItemsPerPageChange={pagination.setItemsPerPage}
          />
        </div>
      )}
    </div>
  );
}
