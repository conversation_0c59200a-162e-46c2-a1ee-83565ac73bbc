'use client';

import { useState, useEffect } from 'react';
import MarkupList from './MarkupList';
import MarkupAddEdit from './MarkupAddEdit';
import { getAllMarkups, createMarkup, updateMarkup, deleteMarkup } from '../services/markup.service';
import { Markup } from '../models/markup.model';

export default function MarkupMaster() {
  const [markups, setMarkups] = useState<Markup[]>([]);
  const [selectedMarkup, setSelectedMarkup] = useState<Markup | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');
  const [lastUpdateTime, setLastUpdateTime] = useState('--:--:--');
  const [nextId, setNextId] = useState(5); // Start from 5 since we have 4 mock items
  const [filters, setFilters] = useState({
    search: '',
    providerType: '',
    status: '',
    type: ''
  });

  // Set initial time on client side
  useEffect(() => {
    setLastUpdateTime(new Date().toLocaleTimeString());
  }, []);

  useEffect(() => {
    fetchMarkups();
  }, []);

  const fetchMarkups = async () => {
    try {
      const fetchedMarkups = await getAllMarkups();
      setMarkups(fetchedMarkups);
      setLastUpdateTime(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Error fetching markups:', error);
      // Fallback to mock data
      const mockMarkups: Markup[] = [
      {
        id: 'markup_1',
        name: 'Standard Hotel Markup',
        providerType: 'hotel',
        status: 'active',
        type: 'percentage',
        value: 15,
        description: 'Standard markup applied to all hotel bookings',
        applicableRegions: ['North America', 'Europe'],
        applicableHotels: [],
        applicableProviders: [],
        validFrom: '2024-01-01T00:00:00Z',
        validTo: '2024-12-31T23:59:59Z',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        createdBy: '<EMAIL>',
        lastModifiedBy: '<EMAIL>',
        priority: 1
      },
      {
        id: 'markup_2',
        name: 'Provider Commission',
        providerType: 'provider',
        status: 'active',
        type: 'rate',
        value: 25,
        currency: 'USD',
        description: 'Fixed rate commission for provider bookings',
        applicableRegions: [],
        applicableHotels: [],
        applicableProviders: ['provider_123', 'provider_456'],
        validFrom: '2024-02-01T00:00:00Z',
        createdAt: '2024-02-01T00:00:00Z',
        updatedAt: '2024-02-01T00:00:00Z',
        createdBy: '<EMAIL>',
        lastModifiedBy: '<EMAIL>',
        priority: 2
      },
      {
        id: 'markup_3',
        name: 'Common Service Fee',
        providerType: 'common markup',
        status: 'active',
        type: 'percentage',
        value: 5,
        description: 'Common service fee applied to all bookings',
        applicableRegions: [],
        applicableHotels: [],
        applicableProviders: [],
        validFrom: '2024-01-01T00:00:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-10T14:20:00Z',
        createdBy: '<EMAIL>',
        lastModifiedBy: '<EMAIL>',
        priority: 3
      },
      {
        id: 'markup_4',
        name: 'Seasonal Markup',
        providerType: 'hotel',
        status: 'inactive',
        type: 'percentage',
        value: 20,
        description: 'Higher markup for peak season bookings',
        applicableRegions: ['Europe'],
        applicableHotels: ['hotel_789'],
        applicableProviders: [],
        validFrom: '2024-06-01T00:00:00Z',
        validTo: '2024-08-31T23:59:59Z',
        createdAt: '2024-05-15T00:00:00Z',
        updatedAt: '2024-05-20T09:15:00Z',
        createdBy: '<EMAIL>',
        lastModifiedBy: '<EMAIL>',
        priority: 1
      }
      ];
      setMarkups(mockMarkups);
      setLastUpdateTime(new Date().toLocaleTimeString());
    }
  };

  const filteredMarkups = markups.filter(markup => {
    const matchesSearch = markup.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                         markup.description?.toLowerCase().includes(filters.search.toLowerCase());
    const matchesProviderType = !filters.providerType || markup.providerType === filters.providerType;
    const matchesStatus = !filters.status || markup.status === filters.status;
    const matchesType = !filters.type || markup.type === filters.type;

    return matchesSearch && matchesProviderType && matchesStatus && matchesType;
  });

  const handleAddMarkup = () => {
    setSelectedMarkup(null);
    setFormMode('add');
    setIsFormOpen(true);
  };

  const handleEditMarkup = (markup: Markup) => {
    setSelectedMarkup(markup);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  const handleDeleteMarkup = async (markup: Markup) => {
    if (confirm(`Are you sure you want to delete "${markup.name}"?`)) {
      try {
        await deleteMarkup(markup.id);
        setMarkups(prev => prev.filter(m => m.id !== markup.id));
        setLastUpdateTime(new Date().toLocaleTimeString());
      } catch (error) {
        console.error('Error deleting markup:', error);
        // Fallback to local delete
        setMarkups(prev => prev.filter(m => m.id !== markup.id));
        setLastUpdateTime(new Date().toLocaleTimeString());
      }
    }
  };

  const handleSaveMarkup = async (markupData: Partial<Markup>) => {
    try {
      if (formMode === 'add') {
        const newMarkup = await createMarkup({
          ...markupData,
          createdBy: '<EMAIL>',
          priority: markupData.priority || 1
        } as any);
        setMarkups(prev => [...prev, newMarkup]);
        setNextId(prev => prev + 1);
      } else if (selectedMarkup) {
        const updatedMarkup = await updateMarkup(selectedMarkup.id, {
          ...markupData,
          lastModifiedBy: '<EMAIL>'
        });
        setMarkups(prev => prev.map(markup =>
          markup.id === selectedMarkup.id ? updatedMarkup : markup
        ));
      }
      setLastUpdateTime(new Date().toLocaleTimeString());
      setIsFormOpen(false);
      setSelectedMarkup(null);
    } catch (error) {
      console.error('Error saving markup:', error);
      // Fallback to local update
      if (formMode === 'add') {
        const now = new Date();
        const newMarkup: Markup = {
          ...markupData,
          id: `markup_${nextId}`,
          createdAt: now.toISOString(),
          updatedAt: now.toISOString(),
          createdBy: '<EMAIL>',
          lastModifiedBy: '<EMAIL>'
        } as Markup;
        setMarkups(prev => [...prev, newMarkup]);
        setNextId(prev => prev + 1);
      } else {
        const now = new Date();
        setMarkups(prev => prev.map(markup =>
          markup.id === selectedMarkup?.id
            ? { ...markup, ...markupData, updatedAt: now.toISOString(), lastModifiedBy: '<EMAIL>' }
            : markup
        ));
      }
      setLastUpdateTime(new Date().toLocaleTimeString());
      setIsFormOpen(false);
      setSelectedMarkup(null);
    }
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedMarkup(null);
  };

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search markups..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>

          <select
            value={filters.providerType}
            onChange={(e) => setFilters(prev => ({ ...prev, providerType: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Provider Types</option>
            <option value="common markup">Common Markup</option>
            <option value="provider">Provider</option>
            <option value="hotel">Hotel</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>

          <select
            value={filters.type}
            onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Types</option>
            <option value="percentage">Percentage</option>
            <option value="rate">Rate</option>
          </select>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            onClick={handleAddMarkup}
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
          >
            <i className="ri-add-line mr-2"></i>
            Add Markup
          </button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredMarkups.length} of {markups.length} markups
        </p>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <i className="ri-refresh-line"></i>
          <span>Last updated: {lastUpdateTime}</span>
        </div>
      </div>

      {/* Markup List */}
      <MarkupList
        markups={filteredMarkups}
        onEdit={handleEditMarkup}
        onDelete={handleDeleteMarkup}
      />

      {/* Add/Edit Modal */}
      <MarkupAddEdit
        markup={selectedMarkup}
        isOpen={isFormOpen}
        onSave={handleSaveMarkup}
        onCancel={handleCloseForm}
        mode={formMode}
      />
    </div>
  );
}
