import axiosInstance from "../../api/axiosInstance";
import { Markup, CreateMarkupRequest, UpdateMarkupRequest } from "../models/markup.model";

// Get all markups
export const getAllMarkups = async (retries = 3): Promise<Markup[]> => {
  try {
    const response = await axiosInstance.get<Markup[]>('/markups/');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching markups:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getAllMarkups(retries - 1);
    }

    // Return mock data as fallback
    return getMockMarkups();
  }
};

// Get markup by ID
export const getMarkupById = async (id: string, retries = 3): Promise<Markup> => {
  try {
    const response = await axiosInstance.get<Markup>(`/markups/${id}/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching markup by ID:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getMarkupById(id, retries - 1);
    }

    // Return mock data as fallback
    const mockMarkups = getMockMarkups();
    const markup = mockMarkups.find(m => m.id === id);
    if (!markup) {
      throw new Error(`Markup with ID ${id} not found`);
    }
    return markup;
  }
};

// Create new markup
export const createMarkup = async (markupData: CreateMarkupRequest, retries = 3): Promise<Markup> => {
  try {
    const response = await axiosInstance.post<Markup>('/markups/', markupData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating markup:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createMarkup(markupData, retries - 1);
    }

    // Create mock markup as fallback
    const mockMarkup: Markup = {
      id: Date.now().toString(),
      ...markupData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return mockMarkup;
  }
};

// Update markup
export const updateMarkup = async (id: string, markupData: UpdateMarkupRequest, retries = 3): Promise<Markup> => {
  try {
    const response = await axiosInstance.put<Markup>(`/markups/${id}/`, markupData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating markup:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateMarkup(id, markupData, retries - 1);
    }

    // Return updated mock markup as fallback
    const existingMarkup = await getMarkupById(id);
    const updatedMarkup: Markup = {
      ...existingMarkup,
      ...markupData,
      updatedAt: new Date().toISOString(),
    };
    return updatedMarkup;
  }
};

// Delete markup
export const deleteMarkup = async (id: string, retries = 3): Promise<void> => {
  try {
    await axiosInstance.delete(`/markups/${id}/`);
  } catch (error: any) {
    console.error('Error deleting markup:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return deleteMarkup(id, retries - 1);
    }

    throw error;
  }
};

// Update markup status
export const updateMarkupStatus = async (id: string, status: Markup['status'], retries = 3): Promise<Markup> => {
  try {
    const response = await axiosInstance.patch<Markup>(`/markups/${id}/status/`, { status });
    return response.data;
  } catch (error: any) {
    console.error('Error updating markup status:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateMarkupStatus(id, status, retries - 1);
    }

    // Return updated mock markup as fallback
    const existingMarkup = await getMarkupById(id);
    const updatedMarkup: Markup = {
      ...existingMarkup,
      status,
      updatedAt: new Date().toISOString(),
    };
    return updatedMarkup;
  }
};

// Search markups
export const searchMarkups = async (query: string, filters?: any, retries = 3): Promise<Markup[]> => {
  try {
    const params = new URLSearchParams();
    if (query) params.append('search', query);
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key]) params.append(key, filters[key]);
      });
    }

    const response = await axiosInstance.get<Markup[]>(`/markups/search/?${params.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error searching markups:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return searchMarkups(query, filters, retries - 1);
    }

    // Return filtered mock data as fallback
    const mockMarkups = getMockMarkups();
    return mockMarkups.filter(markup => 
      markup.name.toLowerCase().includes(query.toLowerCase()) ||
      markup.description?.toLowerCase().includes(query.toLowerCase())
    );
  }
};

// Get markups by provider type
export const getMarkupsByProviderType = async (providerType: string, retries = 3): Promise<Markup[]> => {
  try {
    const response = await axiosInstance.get<Markup[]>(`/markups/provider-type/${providerType}/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching markups by provider type:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getMarkupsByProviderType(providerType, retries - 1);
    }

    // Return filtered mock data as fallback
    const mockMarkups = getMockMarkups();
    return mockMarkups.filter(markup => markup.providerType === providerType);
  }
};

// Apply markup to rate
export const applyMarkupToRate = async (markupId: string, baseRate: number, retries = 3): Promise<{ finalRate: number; markupAmount: number }> => {
  try {
    const response = await axiosInstance.post<{ finalRate: number; markupAmount: number }>(`/markups/${markupId}/apply/`, { baseRate });
    return response.data;
  } catch (error: any) {
    console.error('Error applying markup to rate:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return applyMarkupToRate(markupId, baseRate, retries - 1);
    }

    // Calculate markup locally as fallback
    const markup = await getMarkupById(markupId);
    let markupAmount = 0;
    let finalRate = baseRate;

    if (markup.type === 'percentage') {
      markupAmount = (baseRate * markup.value) / 100;
    } else if (markup.type === 'rate') {
      markupAmount = markup.value;
    }

    finalRate = baseRate + markupAmount;
    return { finalRate, markupAmount };
  }
};

// Mock data for fallback
const getMockMarkups = (): Markup[] => [
  {
    id: 'markup_1',
    name: 'Hotel Provider Markup',
    providerType: 'hotel',
    status: 'active',
    type: 'percentage',
    value: 10,
    description: 'Standard markup for hotel bookings',
    applicableRegions: ['Asia', 'Europe'],
    applicableHotels: ['hotel_1', 'hotel_2'],
    applicableProviders: ['provider_1'],
    validFrom: '2024-01-01T00:00:00Z',
    validTo: '2024-12-31T23:59:59Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    createdBy: '<EMAIL>',
    lastModifiedBy: '<EMAIL>',
    priority: 1
  },
  {
    id: 'markup_2',
    name: 'Provider Commission',
    providerType: 'provider',
    status: 'active',
    type: 'rate',
    value: 25,
    description: 'Fixed commission for provider bookings',
    applicableRegions: [],
    applicableHotels: [],
    applicableProviders: ['provider_2', 'provider_3'],
    validFrom: '2024-01-01T00:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-12T14:45:00Z',
    createdBy: '<EMAIL>',
    lastModifiedBy: '<EMAIL>',
    priority: 2
  },
  {
    id: 'markup_3',
    name: 'Common Service Fee',
    providerType: 'common markup',
    status: 'active',
    type: 'percentage',
    value: 5,
    description: 'Common service fee applied to all bookings',
    applicableRegions: [],
    applicableHotels: [],
    applicableProviders: [],
    validFrom: '2024-01-01T00:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T14:20:00Z',
    createdBy: '<EMAIL>',
    lastModifiedBy: '<EMAIL>',
    priority: 3
  },
  {
    id: 'markup_4',
    name: 'Seasonal Markup',
    providerType: 'hotel',
    status: 'inactive',
    type: 'percentage',
    value: 15,
    description: 'Seasonal markup for peak periods',
    applicableRegions: ['Asia'],
    applicableHotels: [],
    applicableProviders: [],
    validFrom: '2024-06-01T00:00:00Z',
    validTo: '2024-08-31T23:59:59Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-18T09:15:00Z',
    createdBy: '<EMAIL>',
    lastModifiedBy: '<EMAIL>',
    priority: 4
  }
];
