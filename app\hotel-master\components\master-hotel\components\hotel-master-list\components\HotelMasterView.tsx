'use client';

import { hotelDetail } from '@/app/hotel-master/hotel-master.model';
import React from 'react';
import Image from 'next/image';
import TabbedModal, { TabDefinition } from '@/app/components/ui/ModalWithTabs';

interface HotelViewProps {
  hotel: hotelDetail | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (hotelId: number) => void;
  loading?: boolean;
}

// Moved outside the component to prevent re-creating on every render
const tabs: TabDefinition[] = [
  { id: 'overview', label: 'Overview', icon: 'ri-information-line' },
  { id: 'facilities', label: 'Facilities', icon: 'ri-star-line' },
  { id: 'policies', label: 'Policies', icon: 'ri-file-text-line' },
  { id: 'reviews', label: 'Reviews', icon: 'ri-star-line' },
  { id: 'location', label: 'Location', icon: 'ri-map-pin-line' },
  { id: 'gallery', label: 'Gallery', icon: 'ri-image-line' },
];

export default function HotelMasterView({ hotel, isOpen, onClose, onEdit, loading }: HotelViewProps) {
  if (!hotel) return null;

  const getStatusBadge = () => {
    const config = hotel.isVisible ? 
      { color: 'bg-green-100 text-green-800', icon: 'ri-check-line', label: 'Active' } :
      { color: 'bg-red-100 text-red-800', icon: 'ri-close-line', label: 'Inactive' };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {config.label}
      </span>
    );
  };

  const headerActions = (
    <div className="flex items-center space-x-3">
      {getStatusBadge()}
      <div className="flex items-center space-x-1 px-3 py-1 bg-amber-50 rounded-lg border border-amber-200">
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, i) => {
            const rating = parseInt(hotel.starRating || '0');
            return <i key={i} className={`ri-star-${i < rating ? 'fill' : 'line'} text-amber-500 text-sm`}></i>;
          })}
        </div>
        <span className="text-sm font-medium text-amber-700 ml-1">
          {hotel.starRating || 'N/A'} Star{(parseInt(hotel.starRating || '0')) !== 1 ? 's' : ''}
        </span>
      </div>
      {onEdit && (
        <button
          onClick={() => onEdit(hotel.id)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
        >
          <i className="ri-edit-line mr-2 text-sm"></i>
          Edit Hotel
        </button>
      )}
    </div>
  );

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={hotel.name || 'Unnamed Hotel'}
      subtitle={`${hotel.city || 'Unknown City'}, ${hotel.country || 'Unknown Country'} • ${hotel.HotelType || 'Hotel'}`}
      tabs={tabs}
      size="full"
      headerActions={headerActions}
    >
      <TabbedModal.Content tabId="overview">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3"><i className="ri-building-line text-blue-600 text-lg"></i></div>
                <h3 className="text-lg font-semibold text-gray-900">Hotel Information</h3>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Hotel Name</label><p className="text-gray-900 font-medium">{hotel.name || 'Unnamed Hotel'}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Location</label><p className="text-gray-900">{hotel.address || `${hotel.city}, ${hotel.country}`}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Hotel Type</label><p className="text-gray-900">{hotel.HotelType || 'N/A'}</p></div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Star Rating</label>
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => <i key={i} className={`ri-star-${i < parseInt(hotel.starRating || '0') ? 'fill' : 'line'} text-amber-400`}></i>)}
                      <span className="ml-2 text-gray-600">({hotel.starRating || 'N/A'}/5)</span>
                    </div>
                  </div>
                  {hotel.provider_name && <div><label className="block text-sm font-medium text-gray-500 mb-1">Provider</label><span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{hotel.provider_name}</span></div>}
                </div>
              </div>
            </div>
          </div>
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact</h3>
              <div className="space-y-4">
                {hotel.phones_json && hotel.phones_json.length > 0 && <div className="flex justify-between items-center"><span className="text-gray-600">Phone</span><span className="font-semibold text-gray-900">{hotel.phones_json[0]}</span></div>}
                {hotel.faxes_json && hotel.faxes_json.length > 0 && <div className="flex justify-between items-center"><span className="text-gray-600">Fax</span><span className="font-semibold text-gray-900">{hotel.faxes_json[0]}</span></div>}
                <div className="flex justify-between items-center"><span className="text-gray-600">Last Updated</span><span className="font-semibold text-gray-900">{hotel.data_source_updated_at ? new Date(hotel.data_source_updated_at).toLocaleDateString() : 'N/A'}</span></div>
              </div>
            </div>
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="facilities">
         <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Facilities & Amenities</h3>
            {(hotel.facilities && hotel.facilities.length > 0) ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {hotel.facilities.map((facility) => (
                  <div key={facility.id} className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <i className="ri-check-line text-green-600 mr-2"></i>
                    <span className="text-sm font-medium text-gray-800">{facility.name || 'Unnamed Facility'}</span>
                  </div>
                ))}
              </div>
            ) : (<div className="text-center py-8"><p className="text-gray-500 italic">No facilities information available</p></div>)}
         </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="policies">
        <div className="space-y-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
             <h3 className="text-lg font-semibold text-gray-900 mb-4">Policies</h3>
             {(hotel.policies && hotel.policies.length > 0) ? (
              <div className="space-y-4">
                {hotel.policies.map(policy => (
                  <div key={policy.id}><strong className="capitalize">{policy.name?.replace('_', ' ')}:</strong> {policy.description}</div>
                ))}
              </div>
             ) : (<div className="text-center py-8"><p className="text-gray-500 italic">No policy information available</p></div>)}
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="reviews">
        <div className="text-center py-8"><p className="text-gray-500 italic">Reviews feature coming soon.</p></div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="location">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Location Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div><label className="block text-sm font-medium text-gray-500 mb-1">Country</label><p className="text-gray-900 font-medium">{hotel.country}</p></div>
              <div><label className="block text-sm font-medium text-gray-500 mb-1">City</label><p className="text-gray-900 font-medium">{hotel.city}</p></div>
              <div><label className="block text-sm font-medium text-gray-500 mb-1">State</label><p className="text-gray-900 font-medium">{hotel.stateName || 'N/A'}</p></div>
            </div>
            <div className="space-y-4">
              {hotel.geoLocationInfo && (
                <>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Latitude</label><p className="text-gray-900 font-mono">{hotel.geoLocationInfo.lat}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Longitude</label><p className="text-gray-900 font-mono">{hotel.geoLocationInfo.lon}</p></div>
                </>
              )}
            </div>
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="gallery">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Gallery</h3>
          {(hotel.images && hotel.images.length > 0) ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {hotel.images.map((image) => (
                <div key={image.id} className="group relative overflow-hidden rounded-lg border border-gray-200">
                  <div className="aspect-w-1 aspect-h-1">
                    <Image src={image.image_path} alt={image.alt_text || 'Hotel Image'} layout="fill" objectFit="cover" unoptimized={true} />
                  </div>
                </div>
              ))}
            </div>
          ) : (<div className="text-center py-8"><p className="text-gray-500 italic">No gallery images available</p></div>)}
        </div>
      </TabbedModal.Content>
    </TabbedModal>
  );
}