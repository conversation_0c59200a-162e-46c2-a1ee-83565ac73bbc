'use client';

import React from 'react';


export interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  color: 'blue' | 'emerald' | 'amber' | 'purple';
  description: string;
  subDescription?: string;
  change?: {
    value: number;
  };
}


const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, description, subDescription, change }) => {
  const colorVariants = {
    blue: { gradient: 'from-blue-500 to-blue-600', from: 'from-blue-50', to: 'to-blue-100/50' },
    emerald: { gradient: 'from-emerald-500 to-emerald-600', from: 'from-emerald-50', to: 'to-emerald-100/50' },
    amber: { gradient: 'from-amber-500 to-amber-600', from: 'from-amber-50', to: 'to-amber-100/50' },
    purple: { gradient: 'from-purple-500 to-purple-600', from: 'from-purple-50', to: 'to-purple-100/50' },
  };

  const selectedColor = colorVariants[color];
  const isPositiveChange = change && change.value >= 0;

  return (
    <div className={`bg-white rounded-2xl shadow-sm border border-slate-200 p-5 hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br ${selectedColor.from} ${selectedColor.to} overflow-hidden`}>
      <div className="flex items-start justify-between mb-4">
        <div className={`w-12 h-12 bg-gradient-to-br ${selectedColor.gradient} rounded-xl flex items-center justify-center shadow-lg flex-shrink-0`}>
          <i className={`${icon} text-white text-xl`}></i>
        </div>
        {change && (
           <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${isPositiveChange ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800'} whitespace-nowrap`}>
            <i className={`mr-1 ${isPositiveChange ? 'ri-arrow-up-line' : 'ri-arrow-down-line'}`}></i>
            {Math.abs(change.value)}%
          </div>
        )}
      </div>
      <div className="min-w-0">
        <p className="text-sm font-medium text-slate-600 truncate mb-1">{title}</p>
        <h3 className="text-3xl font-bold text-slate-900 truncate mb-3">{value}</h3>
        <p className="text-xs text-slate-500 truncate">{description}</p>
        {subDescription && <p className="text-xs text-slate-400 truncate">{subDescription}</p>}
      </div>
    </div>
  );
};

// Interface for the main HeaderCards component's props
interface HeaderCardsProps {
  items: StatCardProps[]; // The component now accepts an array of card definitions
  isLoading?: boolean;
}

/**
 * A responsive grid component that dynamically renders statistics cards based on the 'items' prop.
 */
export const HeaderCards: React.FC<HeaderCardsProps> = ({ items = [], isLoading = false }) => {
  const skeletonLayout = "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6";
  
  if (isLoading) {
    // Skeleton loaders now also use the new responsive grid layout
    return (
      <div className={skeletonLayout}>
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="bg-white rounded-2xl p-5 border border-slate-200 animate-pulse">
            <div className="flex justify-between items-start mb-4">
              <div className="w-12 h-12 bg-slate-200 rounded-xl"></div>
              <div className="w-16 h-6 bg-slate-200 rounded-full"></div>
            </div>
            <div className="space-y-2">
              <div className="w-3/4 h-4 bg-slate-200 rounded"></div>
              <div className="w-1/2 h-8 bg-slate-200 rounded"></div>
              <div className="w-full h-3 bg-slate-200 rounded"></div>
              <div className="w-2/3 h-3 bg-slate-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // This is the key change for perfect responsiveness based on item count
  const getGridLayout = (itemCount: number) => {
    let gridClasses = 'grid-cols-1 sm:grid-cols-2'; // Default for mobile and tablets
    
    if (itemCount === 1) {
      gridClasses = 'grid-cols-1';
    } else if (itemCount === 2) {
      gridClasses = 'grid-cols-1 sm:grid-cols-2';
    } else if (itemCount === 3) {
      gridClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    } else { // 4 or more items
      gridClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
    }
    return `grid gap-6 ${gridClasses}`;
  };

  return (
    <div className={getGridLayout(items.length)}>
      {items.map((item, index) => (
        <StatCard key={index} {...item} />
      ))}
    </div>
  );
};

