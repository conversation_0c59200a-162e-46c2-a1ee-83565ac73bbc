
'use client';

import { useState } from 'react';
import HotelMaster from './components/master-hotel/HotelMaster';
import HotelMasterOld from './components/hotel-master/HotelMaster';

export default function HotelManagementPage() {
  const [selectedHotel, setSelectedHotel] = useState<string | null>(null);



  return (
    <div className="flex flex-col">
      {/* Enhanced Professional Header */}
      {/* <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <i className="ri-building-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Hotel Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Manage your hotel properties and room configurations
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700">System Online</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <i className="ri-time-line"></i>
                <span className="whitespace-nowrap">Last sync: 2 minutes ago</span>
              </div>
              <button className="inline-flex items-center px-4 py-2.5 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm whitespace-nowrap">
                <i className="ri-refresh-line mr-2"></i>
                Sync All
              </button>
            </div>
          </div>
        </div>
      </div> */}

      <div className="max-w-full">
          <div className="animate-fade-in">
            <HotelMaster />
          </div>
      </div>
    </div>
  );
}
