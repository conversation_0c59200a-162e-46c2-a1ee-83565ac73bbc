// Hotel status types
export type HotelStatus = 'active' | 'inactive' | 'pending';

// Contract types
export type ContractType = 'static' | 'dynamic';

// Contact details interface
export interface ContactDetails {
  address: string;
  phone: string;
  email: string;
  website?: string;
}

// Geolocation interface
export interface Geolocation {
  latitude: number;
  longitude: number;
}

// Main Hotel interface - Updated to match API response
export interface Hotel {
  id: number;
  hotel_id: string;
  name: string;
  city: string;
  country: string;
  country_code: string | null;
  rating_score: string | null;
  address: string | null;
  fax: string | null;
  phones_json: string[] | null;
  faxes_json: string[] | null;
  attributes_json: HotelAttribute[] | null;
  longitude: string | null;
  latitude: string | null;
  about: string | null;
  type: string | null;
  chain_code: string | null;
  provider_id: string | null;
  provider_hotel_id: string | null;
  provider_name: string | null;
  check_in_time: string | null;
  check_out_time: string | null;
  created_at: string;
  updated_at: string | null;

  // Legacy fields for backward compatibility
  area?: string;
  starRating?: number;
  status?: HotelStatus;
  contractType?: ContractType;
  hotelType?: string;
  provider?: string;
  createdAt?: string;
  updatedAt?: string;
  contactDetails?: ContactDetails;
  geolocation?: Geolocation;
  facilities?: string[];
  checkInTime?: string;
  checkOutTime?: string;
  cancellationPolicy?: string;
  gallery?: string[];
  phones?: string[];
  faxes?: string[];
  chainCode?: string;
  providerId?: string;
  providerName?: string;
  providerHotelId?: string;
}

// Hotel attribute interface
export interface HotelAttribute {
  key: string;
  value: string;
  category?: string;
}

// Pagination interface
export interface Pagination {
  total_items: number;
  total_pages: number;
  current_page: number;
  page_size: number;
  has_next: boolean;
  has_previous: boolean;
  next_page: number | null;
  previous_page: number | null;
}

// Request interfaces for API operations
export interface CreateHotelRequest {
  name: string;
  country: string;
  city: string;
  area: string;
  starRating: number;
  status: HotelStatus;
  contractType: ContractType;
  hotelType: string;
  provider?: string;
  contactDetails: ContactDetails;
  geolocation: Geolocation;
  facilities: string[];
  checkInTime: string;
  checkOutTime: string;
  cancellationPolicy: string;
  gallery?: string[];
  
  // Additional optional fields
  hotel_id?: string;
  about?: string;
  phones?: string[];
  faxes?: string[];
  chainCode?: string;
  providerId?: string;
  providerName?: string;
  providerHotelId?: string;
}

export interface UpdateHotelRequest {
  name?: string;
  country?: string;
  city?: string;
  area?: string;
  starRating?: number;
  status?: HotelStatus;
  contractType?: ContractType;
  hotelType?: string;
  provider?: string;
  contactDetails?: Partial<ContactDetails>;
  geolocation?: Partial<Geolocation>;
  facilities?: string[];
  checkInTime?: string;
  checkOutTime?: string;
  cancellationPolicy?: string;
  gallery?: string[];
  
  // Additional optional fields
  hotel_id?: string;
  about?: string;
  phones?: string[];
  faxes?: string[];
  chainCode?: string;
  providerId?: string;
  providerName?: string;
  providerHotelId?: string;
}

// Filter interface for hotel search
export interface HotelFilters {
  search?: string;
  status?: HotelStatus | '';
  country?: string;
  city?: string;
  starRating?: number | '';
  contractType?: ContractType | '';
  hotelType?: string;
  provider?: string;
}

// Response interfaces - Updated to match API response
export interface HotelListResponse {
  items: Hotel[];
  pagination: Pagination;
}

export interface HotelResponse {
  data: Hotel;
  message?: string;
}

// Error response interface
export interface HotelErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

// Hotel statistics interface
export interface HotelStats {
  totalHotels: number;
  activeHotels: number;
  inactiveHotels: number;
  pendingHotels: number;
  averageStarRating: number;
  topCountries: Array<{
    country: string;
    count: number;
  }>;
  contractTypeDistribution: {
    static: number;
    dynamic: number;
  };
}

// Hotel validation rules
export const HotelValidationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  country: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  city: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  area: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  starRating: {
    required: true,
    min: 1,
    max: 5
  },
  contactDetails: {
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    phone: {
      required: true,
      minLength: 10,
      maxLength: 20
    }
  },
  geolocation: {
    latitude: {
      required: true,
      min: -90,
      max: 90
    },
    longitude: {
      required: true,
      min: -180,
      max: 180
    }
  }
};

// Hotel type options
export const HotelTypes = [
  'Luxury Resort',
  'Business Hotel',
  'Boutique Hotel',
  'Beach Resort',
  'Mountain Lodge',
  'City Hotel',
  'Airport Hotel',
  'Spa Hotel',
  'Extended Stay',
  'Hostel',
  'Motel',
  'Apartment Hotel'
];

// Common facilities options
export const CommonFacilities = [
  'Free WiFi',
  'Swimming Pool',
  'Spa & Wellness',
  'Fitness Center',
  'Restaurant',
  'Bar/Lounge',
  'Room Service',
  'Concierge Service',
  'Business Center',
  'Conference Rooms',
  'Parking',
  'Airport Shuttle',
  'Pet Friendly',
  'Laundry Service',
  '24/7 Front Desk',
  'Air Conditioning',
  'Heating',
  'Non-smoking Rooms',
  'Family Rooms',
  'Disabled Access'
];

// Utility functions for hotel data manipulation
export const HotelUtils = {
  // Format hotel display name
  formatDisplayName: (hotel: Hotel): string => {
    return `${hotel.name} (${hotel.city}, ${hotel.country})`;
  },

  // Get status badge color
  getStatusColor: (status: HotelStatus): string => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Get contract type badge color
  getContractTypeColor: (contractType: ContractType): string => {
    switch (contractType) {
      case 'static':
        return 'bg-blue-100 text-blue-800';
      case 'dynamic':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Format star rating display
  formatStarRating: (rating: number): string => {
    return '★'.repeat(rating) + '☆'.repeat(5 - rating);
  },

  // Validate hotel data
  validateHotel: (hotel: Partial<Hotel>): string[] => {
    const errors: string[] = [];
    
    if (!hotel.name || hotel.name.length < 2) {
      errors.push('Hotel name must be at least 2 characters long');
    }
    
    if (!hotel.country || hotel.country.length < 2) {
      errors.push('Country must be at least 2 characters long');
    }
    
    if (!hotel.city || hotel.city.length < 2) {
      errors.push('City must be at least 2 characters long');
    }
    
    if (!hotel.starRating || hotel.starRating < 1 || hotel.starRating > 5) {
      errors.push('Star rating must be between 1 and 5');
    }
    
    if (hotel.contactDetails?.email && !HotelValidationRules.contactDetails.email.pattern.test(hotel.contactDetails.email)) {
      errors.push('Invalid email format');
    }
    
    return errors;
  }
};
