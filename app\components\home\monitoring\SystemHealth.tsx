'use client';

import React from 'react';

export default function SystemHealth() {
  const healthMetrics = [
    { label: 'API Response Time', value: '89ms', percentage: 75, color: 'emerald' },
    { label: 'Database Performance', value: '99.2%', percentage: 92, color: 'emerald' },
    { label: 'Payment Gateway', value: 'Online', percentage: 100, color: 'emerald', isStatus: true },
    { label: 'CDN Performance', value: '98.7%', percentage: 87, color: 'emerald' }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <h3 className="font-semibold text-slate-900 text-lg">System Health Monitor</h3>
        <div className="flex items-center space-x-2 flex-shrink-0">
          <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-emerald-600 font-medium">Optimal</span>
        </div>
      </div>

      <div className="space-y-4">
        {healthMetrics.map((metric, index) => (
          <div key={index} className="flex justify-between items-center gap-3">
            <span className="text-sm text-slate-600 min-w-0 flex-1 truncate">{metric.label}</span>
            <div className="flex items-center space-x-2 flex-shrink-0">
              <span className="text-sm font-semibold text-emerald-600 whitespace-nowrap">{metric.value}</span>
              {!metric.isStatus ? (
                <div className="w-12 h-2 bg-slate-200 rounded-full">
                  <div
                    className="h-2 bg-emerald-500 rounded-full transition-all duration-300"
                    style={{ width: `${metric.percentage}%` }}
                  ></div>
                </div>
              ) : (
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
