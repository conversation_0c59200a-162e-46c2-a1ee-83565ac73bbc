'use client';

import { useState, useEffect } from 'react';
import Modal from '../../components/ui/Modal';
import { Markup, ProviderType, MarkupStatus, MarkupType } from '../models/markup.model';

interface MarkupAddEditProps {
  markup?: Markup | null;
  isOpen: boolean;
  onSave: (markup: Partial<Markup>) => void;
  onCancel: () => void;
  mode?: 'add' | 'edit';
}

export default function MarkupAddEdit({ markup, isOpen, onSave, onCancel, mode = 'add' }: MarkupAddEditProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    providerType: 'common markup' as ProviderType,
    status: 'active' as MarkupStatus,
    type: 'percentage' as MarkupType
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (markup && mode === 'edit') {
      setFormData({
        name: markup.name || '',
        providerType: markup.providerType || 'common markup',
        status: markup.status || 'active',
        type: markup.type || 'percentage'
      });
    } else {
      setFormData({
        name: '',
        providerType: 'common markup',
        status: 'active',
        type: 'percentage'
      });
    }
    setErrors({});
  }, [markup, mode, isOpen]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (formData.name) {
        setIsAutoSaving(true);
        setTimeout(() => setIsAutoSaving(false), 1000);
      }
    }, 3000);

    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Markup name is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const now = new Date();
      const dataToSave = {
        ...formData,
        updatedAt: now.toISOString(),
        ...(mode === 'add' ? {} : { id: markup?.id }),
        ...(mode === 'add' && {
          id: `markup_${now.getTime()}`,
          createdAt: now.toISOString()
        })
      };
      await onSave(dataToSave);
    } catch (error) {
      console.error('Error saving markup:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const providerTypes: { value: ProviderType; label: string }[] = [
    { value: 'common markup', label: 'Common Markup' },
    { value: 'provider', label: 'Provider' },
    { value: 'hotel', label: 'Hotel' }
  ];

  const markupTypes: { value: MarkupType; label: string }[] = [
    { value: 'percentage', label: 'Percentage' },
    { value: 'rate', label: 'Rate' }
  ];

  const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY'];



  const isEditMode = mode === 'edit';
  const formTitle = isEditMode ? `Edit Markup: ${markup?.name}` : 'Add New Markup';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onCancel}
      title={formTitle}
      subtitle={isEditMode ? 'Update markup information and settings' : 'Create a new markup with all necessary details'}
      size="xl"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-2">
          {isAutoSaving && (
            <div className="flex items-center text-sm text-green-600">
              <i className="ri-save-line mr-1"></i>
              Auto-saved
            </div>
          )}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            isEditMode ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
          }`}>
            {isEditMode ? 'Edit Mode' : 'Add Mode'}
          </span>
        </div>
      }
      footer={
        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm text-gray-500">
            <i className="ri-save-line mr-1"></i>
            Auto-saved
          </div>
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="inline-flex items-center px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              form="markup-form"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEditMode ? 'Update Markup' : 'Create Markup'
              )}
            </button>
          </div>
        </div>
      }
    >
      {/* Form Content */}
      <div className="flex-1 overflow-y-auto">
        <form id="markup-form" onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="Enter markup name"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <i className="ri-error-warning-line mr-1"></i>
                    {errors.name}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Provider Type *
                </label>
                <select
                  value={formData.providerType}
                  onChange={(e) => handleInputChange('providerType', e.target.value as ProviderType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  {providerTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status *
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value as MarkupStatus)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value as MarkupType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  {markupTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </form>
      </div>
      </Modal>
    );
  }
