# Dashboard Layout Structure

## Overview
The dashboard now has a consistent layout structure that applies to all pages in the application. The layout consists of three main components:

1. **Header (TopNavigation)** - Fixed at the top
2. **Sidebar** - Collapsible navigation on the left
3. **Content Area** - Main content that adapts to all child pages

## Layout Structure

```
┌─────────────────────────────────────────────────────────┐
│                    Header (TopNavigation)               │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   Sidebar   │            Content Area                   │
│             │           (children)                      │
│             │                                           │
│             │                                           │
│             │                                           │
│             │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

## Implementation Details

### Root Layout (`app/layout.tsx`)
- Contains the overall layout structure
- Imports and renders TopNavigation and Sidebar components
- Wraps all page content in the main content area
- Applies to all pages automatically

### Header Features
- Fixed position at the top
- Contains logo, search, notifications, and user profile
- Responsive design that adapts to mobile screens
- System status indicators and quick stats

### Sidebar Features
- Collapsible navigation menu
- Responsive behavior (auto-collapse on mobile)
- Active state management
- Future module placeholders
- Smooth animations and transitions

### Content Area
- Flexible layout that adapts to content
- Proper overflow handling
- Responsive padding and margins
- Works with all child pages

## Responsive Behavior

### Desktop (≥768px)
- Full sidebar visible
- Complete header with all elements
- Optimal spacing and typography

### Mobile (<768px)
- Sidebar auto-collapses to icon-only view
- Header shows essential elements only
- Touch-friendly interface elements
- Optimized typography sizes

## CSS Variables
The layout uses CSS custom properties for consistent theming:

- `--color-header-bg` - Header background color
- `--color-sidebar-bg` - Sidebar background color
- `--color-text-primary` - Primary text color
- `--color-border` - Border colors
- And many more for comprehensive theming

## Dark Mode Support
The layout includes automatic dark mode support based on user's system preferences with appropriate color adjustments for all components.

## Usage
Simply create new pages in the `app` directory and they will automatically inherit this layout structure. No need to import or configure the header and sidebar in individual pages.

Example:
```tsx
// app/new-page/page.tsx
export default function NewPage() {
  return (
    <div className="p-8">
      <h1>Your Page Content</h1>
      {/* This will automatically have header and sidebar */}
    </div>
  );
}
```

## Testing
Visit `/test` to see a demonstration page that shows how the layout works with different content types.
