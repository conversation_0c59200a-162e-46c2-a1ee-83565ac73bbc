'use client';

import { hotelProvider } from '@/app/hotel-provider/hotel-provider.model';
import React, { useMemo, useState }  from 'react';

interface HotelProviderListProps {
  providers: hotelProvider[];
  onEdit: (provider: hotelProvider) => void;
  onDelete: (provider: hotelProvider) => void; // ADDED: New prop for delete functionality
  isLoading?: boolean;
}

export default function HotelProviderList({ providers, onEdit, onDelete, isLoading }: HotelProviderListProps) {
  const [sortField, setSortField] = useState<keyof hotelProvider>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (field: keyof hotelProvider) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);
  };

  const sortedProviders = useMemo(() => {
    if (!providers) return [];
    return [...providers].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [providers, sortField, sortDirection]);

  const maskApiKey = (apiKey: string) => {
    if (!apiKey || apiKey.length <= 8) return '••••••••';
    return `${apiKey.substring(0, 4)}••••••••${apiKey.substring(apiKey.length - 4)}`;
  };

  const TableHeader = ({ title, field }: { title: string; field: keyof hotelProvider }) => (
    <th
      scope="col"
      className="px-6 py-4 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100 transition-colors"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center">
        {title}
        {sortField === field && (
          <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1.5 text-base`}></i>
        )}
      </div>
    </th>
  );
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-200">
          <thead className="bg-slate-50">
            <tr>
              <TableHeader title="Provider Name" field="name" />
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">API Key</th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">Account ID</th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">Base URL</th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">Channel ID</th>
              <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-slate-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-slate-200">
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <tr key={index}>
                  <td className="px-6 py-4" colSpan={6}>
                    <div className="h-6 bg-slate-200 rounded animate-pulse"></div>
                  </td>
                </tr>
              ))
            ) : sortedProviders.length > 0 ? (
              sortedProviders.map((provider) => (
                <tr key={provider.id} className="hover:bg-slate-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-md">
                          <i className="ri-building-line text-white"></i>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-semibold text-slate-900">{provider.name}</div>
                        <div className="text-sm text-slate-500">{provider.channel_id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-slate-900 font-mono tracking-wider">{maskApiKey(provider.api_key)}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-slate-700 truncate max-w-xs">{provider.account_id}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-700 max-w-xs truncate" title={provider.base_url}>
                      {provider.base_url}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-slate-700">{provider.channel_id}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-1">
                      <button onClick={() => onEdit(provider)} className="w-9 h-9 flex items-center justify-center text-slate-400 hover:text-blue-600 hover:bg-blue-100 rounded-lg transition-colors" title="Edit Provider">
                        <i className="ri-pencil-line text-lg"></i>
                      </button>
                      {/* ADDED: Delete button and functionality */}
                      <button onClick={() => onDelete(provider)} className="w-9 h-9 flex items-center justify-center text-slate-400 hover:text-red-600 hover:bg-red-100 rounded-lg transition-colors" title="Delete Provider">
                        <i className="ri-delete-bin-line text-lg"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6}>
                  <div className="text-center py-16">
                    <div className="w-16 h-16 mx-auto bg-slate-100 rounded-full flex items-center justify-center mb-4">
                      <i className="ri-search-line text-3xl text-slate-400"></i>
                    </div>
                    <h3 className="text-lg font-medium text-slate-800 mb-1">No Providers Found</h3>
                    <p className="text-slate-500">Your search or filter criteria did not match any providers.</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

