'use client';

import React from 'react';
import { BookingStats as BookingStatsType } from '../types';

type ColorKey = 'blue' | 'emerald' | 'purple' | 'amber' | 'cyan' | 'rose';
type ChangeType = 'positive' | 'negative';

interface StatItem {
  title: string;
  value: string;
  change: string;
  changeType: ChangeType;
  icon: string;
  color: ColorKey;
  trend: string;
  subtitle: string;
  additionalInfo: string;
}

interface BookingStatsProps {
  stats?: BookingStatsType;
}

export default function BookingStats({ stats }: BookingStatsProps) {
  // Mock data - in real app, this would come from props or API
  const mockStats: BookingStatsType = stats || {
    totalBookings: 1247,
    completedBookings: 892,
    upcomingBookings: 284,
    cancelledBookings: 71,
    totalRevenue: 485600,
    averageBookingValue: 389.50,
    occupancyRate: 78.5,
    cancellationRate: 5.7
  };

  const statItems: StatItem[] = [
    {
      title: 'Total Bookings',
      value: mockStats.totalBookings.toLocaleString(),
      change: '+12.5%',
      changeType: 'positive',
      icon: 'ri-calendar-check-fill',
      color: 'blue',
      trend: 'up',
      subtitle: 'This month vs last month',
      additionalInfo: `${Math.floor(mockStats.totalBookings * 0.08)} this week`
    },
    {
      title: 'Completed Bookings',
      value: mockStats.completedBookings.toLocaleString(),
      change: '****%',
      changeType: 'positive',
      icon: 'ri-checkbox-circle-fill',
      color: 'emerald',
      trend: 'up',
      subtitle: 'Successfully completed stays',
      additionalInfo: `${((mockStats.completedBookings / mockStats.totalBookings) * 100).toFixed(1)}% completion rate`
    },
    {
      title: 'Upcoming Bookings',
      value: mockStats.upcomingBookings.toLocaleString(),
      change: '+15.2%',
      changeType: 'positive',
      icon: 'ri-time-fill',
      color: 'purple',
      trend: 'up',
      subtitle: 'Future reservations',
      additionalInfo: `${Math.floor(mockStats.upcomingBookings * 0.15)} check-in today`
    },
    {
      title: 'Cancelled Bookings',
      value: mockStats.cancelledBookings.toLocaleString(),
      change: '-2.1%',
      changeType: 'positive',
      icon: 'ri-close-circle-fill',
      color: 'rose',
      trend: 'down',
      subtitle: 'Cancellation rate improved',
      additionalInfo: `${mockStats.cancellationRate}% of total bookings`
    }
  ];

  const colorClasses: Record<ColorKey, string> = {
    blue: 'from-blue-500 to-blue-600',
    emerald: 'from-emerald-500 to-emerald-600',
    purple: 'from-purple-500 to-purple-600',
    amber: 'from-amber-500 to-amber-600',
    cyan: 'from-cyan-500 to-cyan-600',
    rose: 'from-rose-500 to-rose-600'
  };

  const backgroundColors: Record<ColorKey, string> = {
    blue: 'bg-gradient-to-br from-blue-50 to-blue-100/50',
    emerald: 'bg-gradient-to-br from-emerald-50 to-emerald-100/50',
    purple: 'bg-gradient-to-br from-purple-50 to-purple-100/50',
    amber: 'bg-gradient-to-br from-amber-50 to-amber-100/50',
    cyan: 'bg-gradient-to-br from-cyan-50 to-cyan-100/50',
    rose: 'bg-gradient-to-br from-rose-50 to-rose-100/50'
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6">
      {statItems.map((stat, index) => (
        <div key={index} className={`bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow ${backgroundColors[stat.color]} overflow-hidden`}>
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <div className={`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ${colorClasses[stat.color]} rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0`}>
              <i className={`${stat.icon} text-white text-lg sm:text-xl`}></i>
            </div>
            <div className="text-right ml-2">
              <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${
                stat.changeType === 'positive' ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
              } whitespace-nowrap`}>
                <i className={`${stat.changeType === 'positive' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'} mr-1`}></i>
                {stat.change}
              </div>
            </div>
          </div>

          <div className="mb-3 min-w-0">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate">{stat.value}</h3>
            <p className="text-sm font-medium text-slate-600 truncate">{stat.title}</p>
          </div>

          <div className="space-y-1 min-w-0">
            <p className="text-xs text-slate-500 truncate">{stat.subtitle}</p>
            <p className="text-xs text-slate-400 truncate">{stat.additionalInfo}</p>
          </div>

          {/* Subtle background pattern */}
          <div className="absolute top-0 right-0 w-20 h-20 opacity-10 overflow-hidden">
            <div className={`w-full h-full bg-gradient-to-br ${colorClasses[stat.color]} transform rotate-12 translate-x-6 -translate-y-6 rounded-2xl`}></div>
          </div>
        </div>
      ))}
    </div>
  );
}
