import PageSectionHeader from '@/app/components/ui/PageSectionHeader'
import React, { useState } from 'react'
import HotelMasterList from './components/hotel-master-list/HotelMasterList'
import MasterHotelAddEdit from './components/hotel-master-list/components/HotelMasterAddEdit';
import { hotelDetail } from '../../hotel-master.model';
import Modal from '@/app/components/ui/Modal';

function HotelMaster() {

  const [totalItems, setTotalItems] = useState<number>(100);
  const [isAddHotelOpen, setIsAddHotelOpen] = useState<boolean>(false);


  const handleOnSaveHotel = (hotelData: Partial<hotelDetail>) => {
    console.log(hotelData);
    
  }

  const handleOnCloseHotel = () => {
    setIsAddHotelOpen(false);
  }


  const handleTotalItemChange = (totalItems: number) => {
      setTotalItems(totalItems);
  };
  
  return (
    <div className='flex flex-col justify-center gap-6'>
        <PageSectionHeader
           title='Hotel Master' 
           subtitle='Manage all hotel properties and partnerships' 
           totalItems={totalItems} 
           showAddButton={true} 
           addButtonText='Add Hotel'
           onAddButtonClick={() => setIsAddHotelOpen(true)} 
        />

        <HotelMasterList onTotalItemsChange={handleTotalItemChange} />

        <Modal isOpen={isAddHotelOpen} onClose={handleOnCloseHotel}>
          <MasterHotelAddEdit isOpen={isAddHotelOpen} onSave={handleOnSaveHotel} onClose={handleOnCloseHotel} />
        </Modal>

    </div>
  )
}

export default HotelMaster