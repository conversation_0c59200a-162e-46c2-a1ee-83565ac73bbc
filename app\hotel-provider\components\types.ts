export interface HotelProvider {
  id: string;
  name: string;
  apiKey: string;
  accountId: string;
  baseUrl: string;
  channelId: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  description?: string;
  contactEmail?: string;
  supportPhone?: string;
  lastSyncAt?: string;
  syncStatus?: 'connected' | 'disconnected' | 'error';
}

export type HotelProviderStatus = 'active' | 'inactive';
export type SyncStatus = 'connected' | 'disconnected' | 'error';
