'use client';

import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { getNotificationTemplates } from '../../../nm-service';

interface NotificationSetting {
  id: string;
  company_id: number;
  event_key: string;
  channel: string;
  is_enabled: boolean;
  template_id: string;
  created_at: string;
  updated_at: string;
}

interface NotificationTemplate {
  id: string;
  company_id: number;
  event_key: string;
  channel: string;
  language: string;
  subject: string;
  template_id: string;
  provider?: string;
  content: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface NotificationSettingFormProps {
  setting?: NotificationSetting | null;
  onSave: (settingData: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

export default function NotificationSettingForm({
  setting,
  onSave,
  onCancel,
  loading = false
}: NotificationSettingFormProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState({
    company_id: 101,
    event_key: '',
    channel: 'email',
    is_enabled: true,
    template_id: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Template selection state
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  const [templateSearch, setTemplateSearch] = useState('');
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<NotificationTemplate | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const templateInputRef = useRef<HTMLInputElement>(null);

  // Event key options
  const eventKeyOptions = [
    { value: 'user_signup', label: 'User Signup' },
    { value: 'password_reset', label: 'Password Reset' },
    { value: 'booking_confirmation', label: 'Booking Confirmation' },
    { value: 'booking_cancellation', label: 'Booking Cancellation' },
    { value: 'payment_success', label: 'Payment Success' },
    { value: 'payment_failed', label: 'Payment Failed' },
    { value: 'welcome_email', label: 'Welcome Email' },
    { value: 'account_verification', label: 'Account Verification' },
    { value: 'booking_reminder', label: 'Booking Reminder' },
    { value: 'review_request', label: 'Review Request' }
  ];

  // Channel options
  const channelOptions = [
    { value: 'email', label: 'Email', icon: 'ri-mail-line' },
    { value: 'sms', label: 'SMS', icon: 'ri-message-2-line' },
    { value: 'whatsapp', label: 'WhatsApp', icon: 'ri-whatsapp-line' },
    { value: 'push', label: 'Push', icon: 'ri-notification-line' }
  ];

  // Fetch templates on component mount
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setTemplatesLoading(true);
        const fetchedTemplates = await getNotificationTemplates();
        setTemplates(fetchedTemplates);
      } catch (error) {
        console.error('Error fetching templates:', error);
      } finally {
        setTemplatesLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  useEffect(() => {
    if (setting) {
      setFormData({
        company_id: setting.company_id,
        event_key: setting.event_key,
        channel: setting.channel,
        is_enabled: setting.is_enabled || false,
        template_id: setting.template_id
      });

      // Find and set the selected template
      const template = templates.find(t => t.id === setting.template_id);
      if (template) {
        setSelectedTemplate(template);
        setTemplateSearch(`${template.subject} (${template.event_key} • ${template.channel})`);
      }
    }
  }, [setting, templates]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (formData.event_key && formData.channel && formData.template_id) {
        setIsAutoSaving(true);
        setTimeout(() => setIsAutoSaving(false), 1000);
      }
    }, 3000);

    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (templateInputRef.current && !templateInputRef.current.contains(event.target as Node)) {
        setShowTemplateDropdown(false);
      }
    };

    if (showTemplateDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showTemplateDropdown]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle template selection
  const handleTemplateSelect = (template: NotificationTemplate) => {
    setSelectedTemplate(template);
    setFormData(prev => ({ ...prev, template_id: template.id }));
    setTemplateSearch(`${template.subject} (${template.event_key} • ${template.channel})`);
    setShowTemplateDropdown(false);

    // Clear error
    if (errors.template_id) {
      setErrors(prev => ({ ...prev, template_id: '' }));
    }
  };

  // Filter templates based on search and form data
  const getFilteredTemplates = () => {
    return templates.filter(template => {
      const searchLower = templateSearch.toLowerCase();
      const matchesSearch =
        template.subject.toLowerCase().includes(searchLower) ||
        template.event_key.toLowerCase().includes(searchLower) ||
        template.channel.toLowerCase().includes(searchLower) ||
        template.template_id.toLowerCase().includes(searchLower);

      // Only filter by channel if one is selected, show all templates otherwise
      const matchesChannel = !formData.channel || template.channel === formData.channel;

      // Show both active and inactive templates, but prioritize active ones
      return matchesSearch && matchesChannel;
    });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.event_key) newErrors.event_key = 'Event key is required';
    if (!formData.channel) newErrors.channel = 'Channel is required';
    if (!formData.template_id.trim()) newErrors.template_id = 'Please select a notification template';
    if (!formData.company_id) newErrors.company_id = 'Company ID is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const getEventKeyLabel = (eventKey: string) => {
    const eventLabels: Record<string, string> = {
      user_signup: 'User Signup',
      password_reset: 'Password Reset',
      booking_confirmation: 'Booking Confirmation',
      booking_cancellation: 'Booking Cancellation',
      payment_success: 'Payment Success',
      payment_failed: 'Payment Failed',
      welcome_email: 'Welcome Email',
      account_verification: 'Account Verification',
      booking_reminder: 'Booking Reminder',
      review_request: 'Review Request'
    };
    return eventLabels[eventKey] || eventKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: 'ri-information-line' },
    { id: 'configuration', label: 'Configuration', icon: 'ri-settings-line' }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-1 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  relative px-4 py-3 font-medium text-sm transition-all duration-200 
                  whitespace-nowrap border-b-2 min-w-max
                  ${activeTab === tab.id
                    ? 'text-blue-600 border-blue-600 bg-blue-50/50'
                    : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <div className="flex items-center">
                  <i className={`${tab.icon} mr-2 text-base`}></i>
                  {tab.label}
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Auto-save indicator */}
      {isAutoSaving && (
        <div className="flex-shrink-0 bg-green-50 border-b border-green-200 px-6 py-2">
          <div className="flex items-center text-sm text-green-700">
            <i className="ri-save-line mr-2 animate-pulse"></i>
            Auto-saving...
          </div>
        </div>
      )}

      {/* Form Content */}
      <form id="notification-setting-form" onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar">
        <div className="p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-information-line mr-3 text-blue-600"></i>
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company ID *
                    </label>
                    <input
                      type="number"
                      value={formData.company_id}
                      onChange={(e) => handleInputChange('company_id', parseInt(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.company_id ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter company ID"
                      required
                    />
                    {errors.company_id && (
                      <p className="text-red-500 text-xs mt-1">{errors.company_id}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Event Key *
                    </label>
                    <select
                      value={formData.event_key}
                      onChange={(e) => handleInputChange('event_key', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.event_key ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      <option value="">Select event key</option>
                      {eventKeyOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.event_key && (
                      <p className="text-red-500 text-xs mt-1">{errors.event_key}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Channel *
                    </label>
                    <select
                      value={formData.channel}
                      onChange={(e) => handleInputChange('channel', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.channel ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      {channelOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.channel && (
                      <p className="text-red-500 text-xs mt-1">{errors.channel}</p>
                    )}
                  </div>

                  <div className="relative template-dropdown-container">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notification Template *
                    </label>
                    <div className="relative">
                      <input
                        ref={templateInputRef}
                        type="text"
                        value={templateSearch}
                        onChange={(e) => {
                          setTemplateSearch(e.target.value);
                          setShowTemplateDropdown(true);
                        }}
                        onFocus={() => {
                          if (templateInputRef.current) {
                            const rect = templateInputRef.current.getBoundingClientRect();
                            setDropdownPosition({
                              top: rect.bottom + window.scrollY,
                              left: rect.left + window.scrollX,
                              width: rect.width
                            });
                          }
                          setShowTemplateDropdown(true);
                          if (templates.length === 0) {
                            fetchTemplates();
                          }
                        }}
                        className={`w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors.template_id ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Search and select a template..."
                        required
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        {templatesLoading ? (
                          <i className="ri-loader-line animate-spin text-gray-400"></i>
                        ) : (
                          <i className="ri-search-line text-gray-400"></i>
                        )}
                      </div>


                    </div>

                    {errors.template_id && (
                      <p className="text-red-500 text-xs mt-1">{errors.template_id}</p>
                    )}

                    {selectedTemplate && (
                      <div className="mt-2 p-2 bg-blue-50 rounded-lg">
                        <div className="text-xs text-blue-700">
                          <strong>Selected:</strong> {selectedTemplate.subject}
                        </div>
                        <div className="text-xs text-blue-600 mt-1">
                          ID: {selectedTemplate.id} • {selectedTemplate.event_key} • {selectedTemplate.channel}
                        </div>
                      </div>
                    )}

                    <p className="text-xs text-gray-500 mt-1">
                      Search and select from existing notification templates
                    </p>
                  </div>

                  <div className="md:col-span-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="is_enabled"
                        checked={formData.is_enabled}
                        onChange={(e) => handleInputChange('is_enabled', e.target.checked)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="is_enabled" className="text-sm font-medium text-gray-700">
                        Setting is enabled
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Disabled settings will not trigger notifications
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Configuration Tab */}
          {activeTab === 'configuration' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-6 border border-green-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-settings-line mr-3 text-green-600"></i>
                  Configuration Details
                </h3>
                
                <div className="space-y-6">
                  {/* Event Configuration Preview */}
                  <div className="bg-white rounded-lg p-4 border border-green-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Event Configuration Preview</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-green-50 rounded-lg p-3">
                        <div className="text-xs text-green-500 uppercase tracking-wide">Event</div>
                        <div className="text-sm font-semibold text-green-900 mt-1">
                          {formData.event_key ? getEventKeyLabel(formData.event_key) : 'Select an event'}
                        </div>
                        <div className="text-xs text-green-600 mt-1 font-mono">
                          {formData.event_key || 'event_key'}
                        </div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-3">
                        <div className="text-xs text-green-500 uppercase tracking-wide">Channel</div>
                        <div className="text-sm font-semibold text-green-900 mt-1 capitalize">
                          {formData.channel}
                        </div>
                        <div className="text-xs text-green-600 mt-1">
                          Notifications will be sent via {formData.channel}
                        </div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-3">
                        <div className="text-xs text-green-500 uppercase tracking-wide">Template</div>
                        <div className="text-sm font-semibold text-green-900 mt-1">
                          {selectedTemplate ? selectedTemplate.subject : 'No template selected'}
                        </div>
                        <div className="text-xs text-green-600 mt-1">
                          {selectedTemplate
                            ? `${selectedTemplate.event_key} • ${selectedTemplate.channel} • ${selectedTemplate.language}`
                            : 'Please select a notification template'
                          }
                        </div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-3">
                        <div className="text-xs text-green-500 uppercase tracking-wide">Status</div>
                        <div className="text-sm font-semibold text-green-900 mt-1">
                          {formData.is_enabled ? 'Enabled' : 'Disabled'}
                        </div>
                        <div className="text-xs text-green-600 mt-1">
                          {formData.is_enabled ? 'Will trigger notifications' : 'Will not trigger notifications'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Configuration Summary */}
                  <div className="bg-white rounded-lg p-4 border border-green-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Configuration Summary</h4>
                    <div className="prose prose-sm text-gray-600">
                      <p>
                        When a <strong>{formData.event_key ? getEventKeyLabel(formData.event_key) : '[Event]'}</strong> event
                        occurs for company <strong>{formData.company_id}</strong>,
                        {formData.is_enabled ? (
                          <>
                            {' '}a notification will be sent via <strong>{formData.channel}</strong> using
                            template <strong>"{selectedTemplate ? selectedTemplate.subject : '[No template selected]'}"</strong>
                            {selectedTemplate && (
                              <span className="text-gray-500">
                                {' '}({selectedTemplate.event_key} • {selectedTemplate.channel})
                              </span>
                            )}.
                          </>
                        ) : (
                          <span className="text-red-600"> no notification will be sent because this setting is disabled.</span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </form>

      {/* Portal-based dropdown */}
      {showTemplateDropdown && typeof window !== 'undefined' && createPortal(
        <div
          className="fixed z-[9999] bg-white border border-gray-300 rounded-lg shadow-xl max-h-80 overflow-y-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
        >
          {templatesLoading ? (
            <div className="p-4 text-center text-gray-500">
              <i className="ri-loader-line animate-spin mr-2"></i>
              Loading templates...
            </div>
          ) : getFilteredTemplates().length > 0 ? (
            getFilteredTemplates().map((template) => (
              <div
                key={template.id}
                onClick={() => handleTemplateSelect(template)}
                className="p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 text-sm">
                      {template.subject}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {template.event_key} • {template.channel.toUpperCase()} • {template.language.toUpperCase()}
                    </div>
                    {template.provider && (
                      <div className="text-xs text-blue-600 mt-1">
                        Provider: {template.provider.toUpperCase()}
                      </div>
                    )}
                  </div>
                  <div className="ml-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      template.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {template.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-gray-500">
              {templateSearch ? 'No templates found matching your search' : 'No templates available'}
            </div>
          )}
        </div>,
        document.body
      )}
    </div>
  );
}
