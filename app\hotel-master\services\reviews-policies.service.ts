import axiosInstance from "../../api/axiosInstance";

// Hotel Review interface - Updated to match API response
export interface HotelReview {
  id: number;
  hotel_id: number;
  user_id: number | null;
  rating: string;
  content: string;
  type: string;
  created_at: string;
  updated_at: string | null;

  // Legacy fields for backward compatibility
  guest_name?: string;
  guest_email?: string;
  title?: string;
  comment?: string;
  stay_date?: string;
  review_date?: string;

  verified?: boolean;
  helpful_votes?: number;
  status?: 'pending' | 'approved' | 'rejected';
}

// Hotel Policy interface - Updated to match actual API response
export interface HotelPolicy {
  id: number;
  hotel_id: number;
  name: string; // This is the policy type (check_in, check_out, cancellation, pets, smoking)
  description: string;
  checkin_time: string | null;
  checkout_time: string | null;
  created_at: string;
  updated_at: string | null;
}



// Request interfaces - Updated to match API structure
export interface CreateHotelReviewRequest {
  hotel_id: number;
  user_id?: number;
  rating: string;
  content: string;
  type: string;

  // Legacy fields for backward compatibility
  guest_name?: string;
  guest_email?: string;
  title?: string;
  comment?: string;
  stay_date?: string;

  verified?: boolean;
}

export interface CreateHotelPolicyRequest {
  hotel_id: number;
  name: string; // Policy type: check_in, check_out, cancellation, pets, smoking
  description: string;
  checkin_time?: string;
  checkout_time?: string;
}



// Hotel Reviews API
export const createHotelReview = async (reviewData: CreateHotelReviewRequest, retries = 3): Promise<HotelReview> => {
  try {
    const response = await axiosInstance.post<HotelReview>('/hotel-reviews/', reviewData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating hotel review:', error);

    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotelReview(reviewData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

export const getHotelReviews = async (hotelId: string | number, retries = 3): Promise<HotelReview[]> => {
  try {
    const response = await axiosInstance.get<HotelReview[]>(`/hotels/${hotelId}/reviews/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel reviews:', error);

    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelReviews(hotelId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};

// Hotel Policies API
export const createHotelPolicy = async (policyData: CreateHotelPolicyRequest, retries = 3): Promise<HotelPolicy> => {
  try {
    const response = await axiosInstance.post<HotelPolicy>('/hotel-policies/', policyData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating hotel policy:', error);

    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotelPolicy(policyData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

export const getHotelPolicies = async (hotelId: string, retries = 3): Promise<HotelPolicy[]> => {
  try {
    const response = await axiosInstance.get<HotelPolicy[]>(`/hotels/${hotelId}/policies/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel policies:', error);

    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelPolicies(hotelId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};




