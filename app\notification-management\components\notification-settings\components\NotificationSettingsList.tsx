'use client';

import { useState } from 'react';

interface NotificationSetting {
  id: string;
  company_id: number;
  event_key: string;
  channel: string;
  is_enabled: boolean;
  template_id: string;
  created_at: string;
  updated_at: string;
}

interface NotificationSettingsListProps {
  settings: NotificationSetting[];
  onEdit: (setting: NotificationSetting) => void;
  onView: (setting: NotificationSetting) => void;
  onDelete: (setting: NotificationSetting) => void;
  onCreate: () => void;
  loading?: boolean;
}

export default function NotificationSettingsList({
  settings,
  onEdit,
  onView,
  onDelete,
  onCreate,
  loading = false
}: NotificationSettingsListProps) {

  const getStatusBadge = (isEnabled?: boolean) => {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        isEnabled 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
          isEnabled ? 'bg-green-500' : 'bg-red-500'
        }`}></div>
        {isEnabled ? 'Enabled' : 'Disabled'}
      </span>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'ri-mail-line';
      case 'sms':
        return 'ri-message-2-line';
      case 'push':
        return 'ri-notification-line';
      default:
        return 'ri-send-plane-line';
    }
  };

  const getChannelColor = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'text-blue-600 bg-blue-100';
      case 'sms':
        return 'text-green-600 bg-green-100';
      case 'push':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getEventKeyLabel = (eventKey: string) => {
    const eventLabels: Record<string, string> = {
      user_signup: 'User Signup',
      password_reset: 'Password Reset',
      booking_confirmation: 'Booking Confirmation',
      booking_cancellation: 'Booking Cancellation',
      payment_success: 'Payment Success',
      payment_failed: 'Payment Failed',
      welcome_email: 'Welcome Email',
      account_verification: 'Account Verification',
      booking_reminder: 'Booking Reminder',
      review_request: 'Review Request'
    };
    return eventLabels[eventKey] || eventKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="space-y-6">
      {/* Settings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {settings.map((setting) => (
          <div key={setting.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            {/* Card Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${getChannelColor(setting.channel)}`}>
                    <i className={`${getChannelIcon(setting.channel)} text-xl`}></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {getEventKeyLabel(setting.event_key)}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {setting.channel.toUpperCase()}
                    </p>
                  </div>
                </div>
                {getStatusBadge(setting.is_enabled)}
              </div>

              {/* Template Info */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Template</span>
                  <span className="font-medium text-gray-900 font-mono text-xs truncate max-w-32">
                    {setting.template_id}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Event Key</span>
                  <span className="font-medium text-gray-900 font-mono text-xs">
                    {setting.event_key}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Company</span>
                  <span className="font-medium text-gray-900">
                    {setting.company_id}
                  </span>
                </div>
              </div>
            </div>

            {/* Card Actions */}
            <div className="p-4 bg-gray-50 rounded-b-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <i className="ri-time-line"></i>
                  <span>Updated {new Date(setting.updated_at || '').toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onView(setting)}
                    disabled={loading}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="View Setting"
                  >
                    <i className="ri-eye-line"></i>
                  </button>
                  <button
                    onClick={() => onEdit(setting)}
                    disabled={loading}
                    className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Edit Setting"
                  >
                    <i className="ri-edit-line"></i>
                  </button>
                  <button
                    onClick={() => onDelete(setting)}
                    disabled={loading}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Delete Setting"
                  >
                    <i className="ri-delete-bin-line"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {settings.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-notification-3-line text-2xl text-gray-400"></i>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No settings found</h3>
          <p className="text-gray-600 mb-6">
            Get started by creating your first notification setting
          </p>
          <button
            onClick={onCreate}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i className="ri-add-line mr-2"></i>
            Create Setting
          </button>
        </div>
      )}
    </div>
  );
}
