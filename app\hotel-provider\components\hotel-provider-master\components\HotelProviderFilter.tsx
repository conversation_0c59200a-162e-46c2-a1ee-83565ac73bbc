'use client';

import React from 'react';

export interface HotelProviderFilterState {
  search: string;
  status: 'active' | 'inactive' | '';
}

interface HotelProviderFilterProps {
  filters: HotelProviderFilterState;
  onFilterChange: (newFilters: HotelProviderFilterState) => void;
}

export const HotelProviderFilter: React.FC<HotelProviderFilterProps> = ({ filters, onFilterChange }) => {

  const handleInputChange = (field: keyof HotelProviderFilterState, value: string) => {
    onFilterChange({ ...filters, [field]: value });
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 mb-6">
      <div className="flex flex-col sm:flex-row items-center gap-4">
        {/* Search Input */}
        <div className="relative w-full sm:flex-1">
          <i className="ri-search-line absolute left-3.5 top-1/2 transform -translate-y-1/2 text-slate-400 z-10"></i>
          <input
            type="text"
            placeholder="Search providers by name..."
            value={filters.search}
            onChange={(e) => handleInputChange('search', e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg text-sm text-slate-800 placeholder:text-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
          />
        </div>

        {/* Status Select Dropdown */}
        <div className="relative w-full sm:w-auto">
           <select
            value={filters.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            className="appearance-none w-full sm:w-48 px-4 py-2.5 border border-slate-300 rounded-lg bg-white text-sm text-slate-800 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
           <i className="ri-arrow-down-s-line absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 pointer-events-none"></i>
        </div>
      </div>
    </div>
  );
};

