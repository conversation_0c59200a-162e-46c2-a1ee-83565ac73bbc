import axiosInstance from "../../api/axiosInstance";

// Hotel Price interface - Updated to match API response
export interface HotelPrice {
  id: number;
  hotel_id: number;
  provider_id: string;
  provider_name: string;
  provider_hotel_id: string | null;
  distribution_type: string | null;
  distribution_channel: string | null;
  rate_type: string | null;
  board_basis: string;
  board_basis_type: string;
  base_rate: string;
  total_rate: string;
  published_rate: string;
  published_base_rate: string | null;
  commission: string;
  taxes: string;
  fees: string;
  discounts: string;
  refundable: boolean;
  pay_at_hotel: boolean;
  is_child_converted_to_adult: boolean | null;
  offer_title: string | null;
  offer_description: string | null;
  discount_offer: string | null;
  percentage_discount_offer: string | null;
  currency_code: string;
  created_at: string;
  updated_at: string;

  // Legacy fields for backward compatibility
  price_type?: 'base' | 'seasonal' | 'promotional' | 'dynamic';
  currency?: string;
  amount?: number;
  valid_from?: string;
  valid_to?: string;
  room_type?: string;
  board_type?: string;
  occupancy?: number;
  min_stay?: number;
  max_stay?: number;
  cancellation_policy?: string;
  advance_purchase_days?: number;
  status?: 'active' | 'inactive' | 'expired';
}

// Request interfaces - Updated to match API structure
export interface CreateHotelPriceRequest {
  hotel_id: number;
  provider_id: string;
  provider_name: string;
  provider_hotel_id?: string;
  distribution_type?: string;
  distribution_channel?: string;
  rate_type?: string;
  board_basis: string;
  board_basis_type: string;
  base_rate: string;
  total_rate: string;
  published_rate?: string;
  published_base_rate?: string;
  commission?: string;
  taxes?: string;
  fees?: string;
  discounts?: string;
  refundable: boolean;
  pay_at_hotel: boolean;
  is_child_converted_to_adult?: boolean;
  offer_title?: string;
  offer_description?: string;
  discount_offer?: string;
  percentage_discount_offer?: string;
  currency_code: string;

  // Legacy fields for backward compatibility
  price_type?: 'base' | 'seasonal' | 'promotional' | 'dynamic';
  currency?: string;
  amount?: number;
  valid_from?: string;
  valid_to?: string;
  room_type?: string;
  board_type?: string;
  occupancy?: number;
  min_stay?: number;
  max_stay?: number;
  cancellation_policy?: string;
  advance_purchase_days?: number;
  status?: 'active' | 'inactive' | 'expired';
}

export interface UpdateHotelPriceRequest {
  provider_id?: string;
  provider_name?: string;
  provider_hotel_id?: string;
  distribution_type?: string;
  distribution_channel?: string;
  rate_type?: string;
  board_basis?: string;
  board_basis_type?: string;
  base_rate?: string;
  total_rate?: string;
  published_rate?: string;
  published_base_rate?: string;
  commission?: string;
  taxes?: string;
  fees?: string;
  discounts?: string;
  refundable?: boolean;
  pay_at_hotel?: boolean;
  is_child_converted_to_adult?: boolean;
  offer_title?: string;
  offer_description?: string;
  discount_offer?: string;
  percentage_discount_offer?: string;
  currency_code?: string;

  // Legacy fields for backward compatibility
  price_type?: 'base' | 'seasonal' | 'promotional' | 'dynamic';
  currency?: string;
  amount?: number;
  valid_from?: string;
  valid_to?: string;
  room_type?: string;
  board_type?: string;
  occupancy?: number;
  min_stay?: number;
  max_stay?: number;
  cancellation_policy?: string;
  advance_purchase_days?: number;
  status?: 'active' | 'inactive' | 'expired';
}

// Create hotel price
export const createHotelPrice = async (priceData: CreateHotelPriceRequest, retries = 3): Promise<HotelPrice> => {
  try {
    const response = await axiosInstance.post<HotelPrice>('/hotel-prices/', priceData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating hotel price:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotelPrice(priceData, retries - 1);
    }

    // Throw error if creation fails
    throw error;
  }
};

// Get prices by hotel
export const getPricesByHotel = async (hotelId: string | number, retries = 3): Promise<HotelPrice[]> => {
  try {
    const response = await axiosInstance.get<HotelPrice[]>(`/hotels/${hotelId}/prices/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel prices:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getPricesByHotel(hotelId, retries - 1);
    }

    // Return empty array on error
    return [];
  }
};

// Get price by ID
export const getHotelPriceById = async (priceId: string, retries = 3): Promise<HotelPrice> => {
  try {
    const response = await axiosInstance.get<HotelPrice>(`/hotel-prices/${priceId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching hotel price by ID:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelPriceById(priceId, retries - 1);
    }

    // Throw error if price not found
    throw new Error(`Hotel price with ID ${priceId} not found`);
  }
};

// Update hotel price
export const updateHotelPrice = async (priceId: string, priceData: UpdateHotelPriceRequest, retries = 3): Promise<HotelPrice> => {
  try {
    const response = await axiosInstance.put<HotelPrice>(`/hotel-prices/${priceId}`, priceData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating hotel price:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateHotelPrice(priceId, priceData, retries - 1);
    }

    // Throw error if update fails
    throw error;
  }
};

// Delete hotel price
export const deleteHotelPrice = async (priceId: string, retries = 3): Promise<void> => {
  try {
    await axiosInstance.delete(`/hotel-prices/${priceId}`);
  } catch (error: any) {
    console.error('Error deleting hotel price:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return deleteHotelPrice(priceId, retries - 1);
    }

    throw error;
  }
};


