'use client';

import React from 'react';

export default function TopProperties() {
  const topProperties = [
    {
      name: 'The Ritz-Carlton Downtown',
      bookings: 247,
      revenue: '$189,420',
      rating: 4.9,
      growth: '+28%'
    },
    {
      name: 'Marriott Executive Suites',
      bookings: 198,
      revenue: '$145,830',
      rating: 4.8,
      growth: '+22%'
    },
    {
      name: 'Hilton Garden Inn Premium',
      bookings: 185,
      revenue: '$128,290',
      rating: 4.7,
      growth: '+18%'
    },
    {
      name: 'Courtyard Business Center',
      bookings: 162,
      revenue: '$98,160',
      rating: 4.6,
      growth: '+15%'
    },
    {
      name: 'Holiday Inn Express Plus',
      bookings: 134,
      revenue: '$87,280',
      rating: 4.5,
      growth: '+12%'
    }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <h2 className="text-lg sm:text-xl font-semibold text-slate-900">Top Performing Properties</h2>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap">
          View All Properties
        </button>
      </div>

      <div className="space-y-3 sm:space-y-4">
        {topProperties.map((property, index) => (
          <div key={index} className="flex items-center justify-between p-4 sm:p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors overflow-hidden">
            <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                <i className="ri-building-2-line text-white text-lg sm:text-xl"></i>
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-slate-900 text-sm sm:text-base truncate">{property.name}</h3>
                <div className="flex items-center space-x-3 sm:space-x-4 mt-1">
                  <p className="text-xs sm:text-sm text-slate-600 whitespace-nowrap">{property.bookings} bookings</p>
                  <div className="flex items-center whitespace-nowrap">
                    <i className="ri-star-fill text-amber-400 text-xs sm:text-sm mr-1"></i>
                    <span className="text-xs sm:text-sm text-slate-600">{property.rating}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right ml-3 flex-shrink-0">
              <p className="font-bold text-slate-900 text-sm sm:text-lg">{property.revenue}</p>
              <p className="text-emerald-600 text-xs sm:text-sm font-medium">{property.growth}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
