
'use client';

import React from 'react';

export default function RecentActivity() {
  const activities = [
    {
      id: 1,
      type: 'booking',
      title: 'High-Value Booking Created',
      message: 'Premium suite booking at The Ritz-Carlton',
      user: '<PERSON>',
      time: '2 minutes ago',
      icon: 'ri-vip-crown-line',
      color: 'emerald',
      amount: '$4,250',
      priority: 'high'
    },
    {
      id: 2,
      type: 'payment',
      title: 'Payment Processing Alert',
      message: 'Large transaction processed successfully',
      user: 'Payment System',
      time: '5 minutes ago',
      icon: 'ri-secure-payment-line',
      color: 'blue',
      amount: '$12,850',
      priority: 'medium'
    },
    {
      id: 3,
      type: 'support',
      title: 'Escalated Ticket Resolved',
      message: 'VIP customer issue resolved - 5-star rating',
      user: '<PERSON>',
      time: '12 minutes ago',
      icon: 'ri-customer-service-2-line',
      color: 'purple',
      amount: null,
      priority: 'high'
    },
    {
      id: 4,
      type: 'hotel',
      title: 'Property Partnership',
      message: 'New luxury hotel added to portfolio',
      user: 'Partnership Team',
      time: '25 minutes ago',
      icon: 'ri-building-2-line',
      color: 'amber',
      amount: null,
      priority: 'medium'
    },
    {
      id: 5,
      type: 'system',
      title: 'Performance Optimization',
      message: 'API response time improved by 23%',
      user: 'Engineering Team',
      time: '1 hour ago',
      icon: 'ri-speed-up-line',
      color: 'cyan',
      amount: null,
      priority: 'low'
    },
    {
      id: 6,
      type: 'user',
      title: 'Premium User Registration',
      message: 'Corporate account created with $50K credit',
      user: 'Sales Team',
      time: '2 hours ago',
      icon: 'ri-user-star-line',
      color: 'rose',
      amount: '$50,000',
      priority: 'high'
    }
  ];

  const colorClasses = {
    emerald: 'bg-emerald-100 text-emerald-700 border-emerald-200',
    blue: 'bg-blue-100 text-blue-700 border-blue-200',
    purple: 'bg-purple-100 text-purple-700 border-purple-200',
    amber: 'bg-amber-100 text-amber-700 border-amber-200',
    cyan: 'bg-cyan-100 text-cyan-700 border-cyan-200',
    rose: 'bg-rose-100 text-rose-700 border-rose-200'
  };

  const priorityColors = {
    high: 'bg-red-500',
    medium: 'bg-amber-500',
    low: 'bg-slate-400'
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="min-w-0">
          <h2 className="text-lg sm:text-xl font-semibold text-slate-900">Live Activity Feed</h2>
          <p className="text-sm text-slate-500">Real-time system and business events</p>
        </div>
        <div className="flex items-center space-x-2 flex-shrink-0">
          <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-slate-600">Live</span>
        </div>
      </div>

      <div className="space-y-3 sm:space-y-4 max-h-80 sm:max-h-96 overflow-y-auto scrollbar-thin">
        {activities.map((activity) => (
          <div key={activity.id} className={`p-3 sm:p-4 rounded-xl border-2 transition-all hover:shadow-sm ${colorClasses[activity.color as keyof typeof colorClasses]} overflow-hidden`}>
            <div className="flex items-start justify-between gap-3">
              <div className="flex items-start space-x-3 min-w-0 flex-1">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center border-2 ${colorClasses[activity.color as keyof typeof colorClasses]} flex-shrink-0`}>
                  <i className={`${activity.icon} text-base sm:text-lg`}></i>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <p className="font-semibold text-slate-900 text-sm truncate">{activity.title}</p>
                    <div className={`w-2 h-2 rounded-full ${priorityColors[activity.priority as keyof typeof priorityColors]} flex-shrink-0`}></div>
                  </div>
                  <p className="text-sm text-slate-600 mb-2 line-clamp-2">{activity.message}</p>
                  <div className="flex items-center space-x-3 text-xs text-slate-500 overflow-hidden">
                    <div className="flex items-center space-x-1 min-w-0">
                      <i className="ri-user-line flex-shrink-0"></i>
                      <span className="truncate">{activity.user}</span>
                    </div>
                    <span className="flex-shrink-0">•</span>
                    <div className="flex items-center space-x-1 whitespace-nowrap">
                      <i className="ri-time-line"></i>
                      <span>{activity.time}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="text-right flex-shrink-0">
                {activity.amount && (
                  <div className="font-bold text-slate-900 text-sm mb-1">{activity.amount}</div>
                )}
                <button className="text-xs text-slate-500 hover:text-slate-700 transition-colors whitespace-nowrap">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 sm:mt-6 pt-4 border-t border-slate-200">
        <button className="w-full flex items-center justify-center space-x-2 py-2 sm:py-3 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-xl transition-colors">
          <i className="ri-refresh-line"></i>
          <span>Load More Activities</span>
        </button>
      </div>
    </div>
  );
}