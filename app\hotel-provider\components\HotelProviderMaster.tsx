'use client';

import { useState, useEffect } from 'react';
import HotelProviderList from './HotelProviderList';
import HotelProviderAddEdit from './HotelProviderAddEdit';
import Modal from '../../components/ui/Modal';
import { getAllHotelProviders, updateHotelProvider } from '../services/hotel-provider.service';
import { HotelProvider } from '../models/hotel-provider.model';
import PageSectionHeader from '@/app/components/ui/PageSectionHeader';

export default function HotelProviderMaster() {
  const [providers, setProviders] = useState<HotelProvider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<HotelProvider | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    status: ''
  });

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    try {
      const fetchedProviders = await getAllHotelProviders();
      setProviders(fetchedProviders);
    } catch (error) {
      console.error('Error fetching providers:', error);
      setProviders([]);
    }
  };



  const handleEditProvider = (provider: HotelProvider) => {
    setSelectedProvider(provider);
    setIsFormOpen(true);
  };

  const handleSaveProvider = async (providerData: Partial<HotelProvider>) => {
    if (!selectedProvider) return;

    try {
      const updatedProvider = await updateHotelProvider(selectedProvider.id, providerData);
      setProviders(prev => prev.map(provider =>
        provider.id === selectedProvider.id ? updatedProvider : provider
      ));
      setIsFormOpen(false);
      setSelectedProvider(null);
    } catch (error) {
      console.error('Error updating provider:', error);
      // Fallback to local update
      setProviders(prev => prev.map(provider =>
        provider.id === selectedProvider?.id
          ? { ...provider, ...providerData, updatedAt: new Date().toISOString() }
          : provider
      ));
      setIsFormOpen(false);
      setSelectedProvider(null);
    }
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedProvider(null);
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                         provider.accountId.toLowerCase().includes(filters.search.toLowerCase()) ||
                         provider.channelId.toLowerCase().includes(filters.search.toLowerCase());
    const matchesStatus = !filters.status || provider.status === filters.status;

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <PageSectionHeader 
        title='Hotel Providers' 
        subtitle='Manage hotel provider integrations and API connections' 
        totalItems={filteredProviders.length} 
        showAddButton={true} 
        addButtonText='Add Provider'
        onAddButtonClick={() => setIsFormOpen(true)}
      />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow bg-gradient-to-br from-blue-50 to-blue-100/50 overflow-hidden">
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
              <i className="ri-building-2-fill text-white text-lg sm:text-xl"></i>
            </div>
            <div className="text-right ml-2">
              <div className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-700 whitespace-nowrap">
                <i className="ri-arrow-up-line mr-1"></i>
                +5.2%
              </div>
            </div>
          </div>
          <div className="mb-3 min-w-0">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate">{providers.length}</h3>
            <p className="text-sm font-medium text-slate-600 truncate">Total Providers</p>
          </div>
          <div className="space-y-1 min-w-0">
            <p className="text-xs text-slate-500 truncate">All registered providers</p>
            <p className="text-xs text-slate-400 truncate">Across all channels</p>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow bg-gradient-to-br from-emerald-50 to-emerald-100/50 overflow-hidden">
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
              <i className="ri-checkbox-circle-fill text-white text-lg sm:text-xl"></i>
            </div>
            <div className="text-right ml-2">
              <div className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-700 whitespace-nowrap">
                <i className="ri-arrow-up-line mr-1"></i>
                +8.1%
              </div>
            </div>
          </div>
          <div className="mb-3 min-w-0">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate">
              {providers.filter(p => p.status === 'active').length}
            </h3>
            <p className="text-sm font-medium text-slate-600 truncate">Active Providers</p>
          </div>
          <div className="space-y-1 min-w-0">
            <p className="text-xs text-slate-500 truncate">Currently operational</p>
            <p className="text-xs text-slate-400 truncate">{((providers.filter(p => p.status === 'active').length / providers.length) * 100).toFixed(1)}% of total</p>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow bg-gradient-to-br from-amber-50 to-amber-100/50 overflow-hidden">
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
              <i className="ri-pause-circle-fill text-white text-lg sm:text-xl"></i>
            </div>
            <div className="text-right ml-2">
              <div className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-700 whitespace-nowrap">
                <i className="ri-arrow-down-line mr-1"></i>
                -2.3%
              </div>
            </div>
          </div>
          <div className="mb-3 min-w-0">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate">
              {providers.filter(p => p.status === 'inactive').length}
            </h3>
            <p className="text-sm font-medium text-slate-600 truncate">Inactive Providers</p>
          </div>
          <div className="space-y-1 min-w-0">
            <p className="text-xs text-slate-500 truncate">Temporarily disabled</p>
            <p className="text-xs text-slate-400 truncate">Need attention</p>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow bg-gradient-to-br from-purple-50 to-purple-100/50 overflow-hidden">
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
              <i className="ri-time-fill text-white text-lg sm:text-xl"></i>
            </div>
            <div className="text-right ml-2">
              <div className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-700 whitespace-nowrap">
                <i className="ri-arrow-up-line mr-1"></i>
                +12.4%
              </div>
            </div>
          </div>
          <div className="mb-3 min-w-0">
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate">
              {providers.filter(p => {
                const updatedDate = new Date(p.updatedAt);
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return updatedDate > weekAgo;
              }).length}
            </h3>
            <p className="text-sm font-medium text-slate-600 truncate">Recently Updated</p>
          </div>
          <div className="space-y-1 min-w-0">
            <p className="text-xs text-slate-500 truncate">Updated this week</p>
            <p className="text-xs text-slate-400 truncate">Configuration changes</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search providers..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      {/* Provider List */}
      <HotelProviderList
        providers={filteredProviders}
        onEdit={handleEditProvider}
      />

      {/* Edit Modal */}
      {selectedProvider && (
        <Modal
          isOpen={isFormOpen}
          onClose={handleCloseForm}
          title={`Edit Provider: ${selectedProvider.name}`}
          subtitle="Update provider information and settings"
          size="xl"
          height="auto"
        >
          <HotelProviderAddEdit
            provider={selectedProvider}
            onSave={handleSaveProvider}
            onCancel={handleCloseForm}
            mode="edit"
          />
        </Modal>
      )}
    </div>
  );
}
