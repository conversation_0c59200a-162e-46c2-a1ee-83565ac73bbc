'use client';

import { useState, useEffect } from 'react';

interface NotificationTemplate {
  id: string;
  company_id: number;
  event_key: string;
  channel: string;
  language: string;
  subject: string;
  template_id: string;
  provider?: string;
  content: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface NotificationTemplateFormProps {
  template?: NotificationTemplate | null;
  onSave: (templateData: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

export default function NotificationTemplateForm({
  template,
  onSave,
  onCancel,
  loading = false
}: NotificationTemplateFormProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState({
    company_id: 101,
    event_key: '',
    channel: 'email',
    language: 'en',
    subject: '',
    template_id: '',
    provider: '',
    content: '',
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Event key options
  const eventKeyOptions = [
    { value: 'user_signup', label: 'User Signup' },
    { value: 'password_reset', label: 'Password Reset' },
    { value: 'booking_confirmation', label: 'Booking Confirmation' },
    { value: 'booking_cancellation', label: 'Booking Cancellation' },
    { value: 'payment_success', label: 'Payment Success' },
    { value: 'payment_failed', label: 'Payment Failed' },
    { value: 'welcome_email', label: 'Welcome Email' },
    { value: 'account_verification', label: 'Account Verification' },
    { value: 'booking_reminder', label: 'Booking Reminder' },
    { value: 'review_request', label: 'Review Request' }
  ];

  // Channel options
  const channelOptions = [
    { value: 'email', label: 'Email', icon: 'ri-mail-line' },
    { value: 'sms', label: 'SMS', icon: 'ri-message-2-line' },
    { value: 'whatsapp', label: 'WhatsApp', icon: 'ri-whatsapp-line' },
    { value: 'push', label: 'Push', icon: 'ri-notification-line' }
  ];

  // Language options
  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'it', label: 'Italian' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'ja', label: 'Japanese' },
    { value: 'ko', label: 'Korean' },
    { value: 'zh', label: 'Chinese' }
  ];

  // Provider options
  const providerOptions = [
    { value: '', label: 'Default Provider' },
    { value: 'smtp', label: 'SMTP' },
    { value: 'msg91', label: 'MSG91' },
    { value: 'whatsapp', label: 'WhatsApp' },
    { value: 'fcm', label: 'FCM' }
  ];

  useEffect(() => {
    if (template) {
      setFormData({
        company_id: template.company_id,
        event_key: template.event_key,
        channel: template.channel,
        language: template.language,
        subject: template.subject,
        template_id: template.template_id,
        provider: template.provider || '',
        content: template.content,
        is_active: template.is_active
      });
    }
  }, [template]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (formData.event_key && formData.channel && formData.subject) {
        setIsAutoSaving(true);
        setTimeout(() => setIsAutoSaving(false), 1000);
      }
    }, 3000);

    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Auto-generate template_id when event_key, channel, or language changes (but not for msg91)
    if (field === 'event_key' || field === 'channel' || field === 'language') {
      const updatedData = { ...formData, [field]: value };
      if (updatedData.event_key && updatedData.channel && updatedData.language && updatedData.provider !== 'msg91') {
        const autoTemplateId = `${updatedData.event_key}_${updatedData.channel}_${updatedData.language}`;
        setFormData(prev => ({ ...prev, [field]: value, template_id: autoTemplateId }));
      }
    }

    // Clear template_id when provider changes to msg91 (user should enter manually)
    if (field === 'provider') {
      if (value === 'msg91') {
        setFormData(prev => ({ ...prev, [field]: value, template_id: '' }));
      } else {
        // Auto-generate template_id for non-msg91 providers
        const updatedData = { ...formData, [field]: value };
        if (updatedData.event_key && updatedData.channel && updatedData.language) {
          const autoTemplateId = `${updatedData.event_key}_${updatedData.channel}_${updatedData.language}`;
          setFormData(prev => ({ ...prev, [field]: value, template_id: autoTemplateId }));
        }
      }
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.event_key) newErrors.event_key = 'Event key is required';
    if (!formData.channel) newErrors.channel = 'Channel is required';
    if (!formData.language) newErrors.language = 'Language is required';
    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';
    if (!formData.content.trim()) newErrors.content = 'Content is required';
    if (!formData.company_id) newErrors.company_id = 'Company ID is required';

    // Template ID is only required when provider is msg91
    if (formData.provider === 'msg91' && !formData.template_id.trim()) {
      newErrors.template_id = 'Template ID is required for MSG91 provider';
    }

    // Additional validation rules
    if (formData.subject.length > 200) newErrors.subject = 'Subject must be less than 200 characters';
    if (formData.content.length > 10000) newErrors.content = 'Content must be less than 10,000 characters';
    if (formData.company_id < 1) newErrors.company_id = 'Company ID must be a positive number';

    // Validate template_id format when provided (alphanumeric, underscores, hyphens only)
    if (formData.template_id && !/^[a-zA-Z0-9_-]+$/.test(formData.template_id)) {
      newErrors.template_id = 'Template ID can only contain letters, numbers, underscores, and hyphens';
    }

    // Validate template_id length when provided
    if (formData.template_id && formData.template_id.length > 100) {
      newErrors.template_id = 'Template ID must be less than 100 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return; // Prevent submission during loading
    if (validateForm()) {
      onSave(formData);
    }
  };

  // Extract variables from content
  const extractVariables = (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;
    while ((match = variableRegex.exec(content)) !== null) {
      if (!variables.includes(match[1].trim())) {
        variables.push(match[1].trim());
      }
    }
    return variables;
  };

  const contentVariables = extractVariables(formData.content);

  // Common template variables for suggestions
  const commonVariables = [
    'user_name', 'user_email', 'company_name', 'booking_id', 
    'hotel_name', 'check_in_date', 'check_out_date', 'reset_code',
    'verification_code', 'amount', 'currency', 'payment_method'
  ];

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('content') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = formData.content.substring(0, start) + 
                        `{{${variable}}}` + 
                        formData.content.substring(end);
      handleInputChange('content', newContent);
      
      // Set cursor position after inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4);
      }, 0);
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: 'ri-information-line' },
    { id: 'content', label: 'Content', icon: 'ri-file-text-line' },
    { id: 'settings', label: 'Settings', icon: 'ri-settings-line' }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-1 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  relative px-4 py-3 font-medium text-sm transition-all duration-200 
                  whitespace-nowrap border-b-2 min-w-max
                  ${activeTab === tab.id
                    ? 'text-blue-600 border-blue-600 bg-blue-50/50'
                    : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <div className="flex items-center">
                  <i className={`${tab.icon} mr-2 text-base`}></i>
                  {tab.label}
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Auto-save indicator */}
      {isAutoSaving && (
        <div className="flex-shrink-0 bg-green-50 border-b border-green-200 px-6 py-2">
          <div className="flex items-center text-sm text-green-700">
            <i className="ri-save-line mr-2 animate-pulse"></i>
            Auto-saving...
          </div>
        </div>
      )}

      {/* Form Content */}
      <form id="template-form" onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar">
        <div className="p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-information-line mr-3 text-blue-600"></i>
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company ID *
                    </label>
                    <input
                      type="number"
                      value={formData.company_id}
                      onChange={(e) => handleInputChange('company_id', parseInt(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.company_id ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter company ID"
                      required
                    />
                    {errors.company_id && (
                      <p className="text-red-500 text-xs mt-1">{errors.company_id}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Event Key *
                    </label>
                    <select
                      value={formData.event_key}
                      onChange={(e) => handleInputChange('event_key', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.event_key ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      <option value="">Select event key</option>
                      {eventKeyOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.event_key && (
                      <p className="text-red-500 text-xs mt-1">{errors.event_key}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Channel *
                    </label>
                    <select
                      value={formData.channel}
                      onChange={(e) => handleInputChange('channel', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.channel ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      {channelOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.channel && (
                      <p className="text-red-500 text-xs mt-1">{errors.channel}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Provider
                    </label>
                    <select
                      value={formData.provider}
                      onChange={(e) => handleInputChange('provider', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {providerOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Select provider for this template (optional)
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Language *
                    </label>
                    <select
                      value={formData.language}
                      onChange={(e) => handleInputChange('language', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.language ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      {languageOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.language && (
                      <p className="text-red-500 text-xs mt-1">{errors.language}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Tab */}
          {activeTab === 'content' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-6 border border-green-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-file-text-line mr-3 text-green-600"></i>
                  Template Content
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject *
                    </label>
                    <input
                      type="text"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                        errors.subject ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter template subject"
                      required
                    />
                    {errors.subject && (
                      <p className="text-red-500 text-xs mt-1">{errors.subject}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Content *
                    </label>
                    <textarea
                      id="content"
                      value={formData.content}
                      onChange={(e) => handleInputChange('content', e.target.value)}
                      rows={8}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent font-mono text-sm ${
                        errors.content ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter template content with variables like {{user_name}}"
                      required
                    />
                    {errors.content && (
                      <p className="text-red-500 text-xs mt-1">{errors.content}</p>
                    )}
                  </div>

                  {/* Variable Helper */}
                  <div className="bg-white rounded-lg p-4 border border-green-200">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-semibold text-gray-900">Variable Helper</h4>
                      <span className="text-xs text-gray-500">
                        Click to insert into content
                      </span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {commonVariables.map((variable) => (
                        <button
                          key={variable}
                          type="button"
                          onClick={() => insertVariable(variable)}
                          className="text-left px-2 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors font-mono"
                        >
                          {`{{${variable}}}`}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Detected Variables */}
                  {contentVariables.length > 0 && (
                    <div className="bg-white rounded-lg p-4 border border-green-200">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">
                        Detected Variables ({contentVariables.length})
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {contentVariables.map((variable, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 bg-amber-100 text-amber-800 rounded text-xs font-mono"
                          >
                            {`{{${variable}}}`}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-settings-3-line mr-3 text-purple-600"></i>
                  Template Settings
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Template ID - Only show when provider is msg91 */}
                  {formData.provider === 'msg91' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Provider Template ID *
                      </label>
                      <input
                        type="text"
                        value={formData.template_id}
                        onChange={(e) => handleInputChange('template_id', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent font-mono text-sm ${
                          errors.template_id ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="e.g., MSG91_TEMPLATE_ID"
                        required
                      />
                      {errors.template_id && (
                        <p className="text-red-500 text-xs mt-1">{errors.template_id}</p>
                      )}
                      <p className="text-xs text-gray-500 mt-1">
                        Enter the template ID from your MSG91 account
                      </p>
                    </div>
                  )}

                  <div className="md:col-span-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="is_active"
                        checked={formData.is_active}
                        onChange={(e) => handleInputChange('is_active', e.target.checked)}
                        className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                        Template is active
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Inactive templates will not be used for notifications
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
