//notification template
export interface NotificationTemplate {
  id: string; // auto-generated
  company_id: number; // required
  event_key: string; // required
  channel: string; // required
  language: string; // required
  subject: string; // required
  template_id: string; // required
  provider?: string; // optional
  content: string; // required
  is_active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

//provider config
type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
interface JsonObject {
  [key: string]: JsonValue;
}
type JsonArray = JsonValue[];

export interface ProviderConfig {
  id: string;
  company_id: number; // required
  channel: string; // required
  provider_type: string; // required
  display_name: string; // required
  config: JsonObject; // required - JSONB
  priority: number; // required
  is_active: boolean; // not optional in Go
  retry_limit: number; // not optional in Go
  rate_limit: number; // not optional in Go
  retry_count: number; // not optional in Go
  retry_delay: number; // not optional in Go
  timeout_seconds: number; // not optional in Go
  circuit_breaker_threshold: number; // not optional in Go
  circuit_breaker_timeout: number; // not optional in Go
  health_check_url: string; // required
  health_check_interval: number; // not optional in Go
  last_health_check: string; // time.Time in Go -> ISO string
  health_status: string; // not optional in Go
  cost_per_message: number; // required (float64 in Go)
  monthly_quota: number; // not optional in Go
  current_usage: number; // not optional in Go
  usage_reset_date: string; // DateOnly in Go -> "YYYY-MM-DD" format
  tags: JsonObject; // JSONB - not optional in Go
  metadata: JsonObject; // JSONB - not optional in Go
  created_at: string; // time.Time in Go -> ISO string
  updated_at: string; // time.Time in Go -> ISO string
}

//notification setting
export interface NotificationSetting {
  id: string; // Generated on server
  company_id: number; // required (binding:"required")
  event_key: string; // required (binding:"required")
  channel: string; // required (binding:"required")
  is_enabled: boolean; // bool in Go (default false if omitted)
  template_id: string; // required (binding:"required")
  created_at: string; // time.Time in Go -> ISO string (server-generated)
  updated_at: string; // time.Time in Go -> ISO string (server-generated)
}

// Request types for API operations (excluding server-generated fields)

// Notification Template request types
export interface CreateNotificationTemplateRequest {
  company_id: number;
  event_key: string;
  channel: string;
  language: string;
  subject: string;
  template_id: string;
  provider?: string;
  content: string;
  is_active: boolean;
}

export interface UpdateNotificationTemplateRequest {
  company_id?: number;
  event_key?: string;
  channel?: string;
  language?: string;
  subject?: string;
  template_id?: string;
  provider?: string;
  content?: string;
  is_active?: boolean;
}

// Provider Config request types
export interface CreateProviderConfigRequest {
  company_id: number;
  channel: string;
  provider_type: string;
  display_name: string;
  config: JsonObject;
  priority: number;
  is_active: boolean;
  retry_limit: number;
  rate_limit: number;
  retry_count: number;
  retry_delay: number;
  timeout_seconds: number;
  circuit_breaker_threshold: number;
  circuit_breaker_timeout: number;
  health_check_url: string;
  health_check_interval: number;
  last_health_check: string;
  health_status: string;
  cost_per_message: number;
  monthly_quota: number;
  current_usage: number;
  usage_reset_date: string;
  tags: JsonObject;
  metadata: JsonObject;
}

export interface UpdateProviderConfigRequest {
  company_id?: number;
  channel?: string;
  provider_type?: string;
  display_name?: string;
  config?: JsonObject;
  priority?: number;
  is_active?: boolean;
  retry_limit?: number;
  rate_limit?: number;
  retry_count?: number;
  retry_delay?: number;
  timeout_seconds?: number;
  circuit_breaker_threshold?: number;
  circuit_breaker_timeout?: number;
  health_check_url?: string;
  health_check_interval?: number;
  last_health_check?: string;
  health_status?: string;
  cost_per_message?: number;
  monthly_quota?: number;
  current_usage?: number;
  usage_reset_date?: string;
  tags?: JsonObject;
  metadata?: JsonObject;
}

// Notification Setting request types
export interface CreateNotificationSettingRequest {
  company_id: number;
  event_key: string;
  channel: string;
  is_enabled: boolean;
  template_id: string;
}

export interface UpdateNotificationSettingRequest {
  company_id?: number;
  event_key?: string;
  channel?: string;
  is_enabled?: boolean;
  template_id?: string;
}
