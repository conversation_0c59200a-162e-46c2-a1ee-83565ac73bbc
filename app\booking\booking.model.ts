// Booking status types
export type BookingStatus = 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled' | 'no_show';

// Payment status types
export type PaymentStatus = 'pending' | 'paid' | 'partial' | 'refunded' | 'failed';

// Booking source types
export type BookingSource = 'direct' | 'booking.com' | 'expedia' | 'agoda' | 'airbnb' | 'other';

// Main Booking interface
export interface Booking {
  id: string;
  bookingReference: string;
  hotelId: string;
  hotelName: string;
  roomId: string;
  roomName: string;
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  checkInDate: string;
  checkOutDate: string;
  nights: number;
  adults: number;
  children: number;
  status: BookingStatus;
  totalAmount: number;
  currency: string;
  paymentStatus: PaymentStatus;
  bookingSource: BookingSource;
  specialRequests?: string;
  cancellationReason?: string;
  cancelledAt?: string;
  checkedInAt?: string;
  checkedOutAt?: string;
  createdAt: string;
  updatedAt: string;
  
  // Additional guest information
  guestAddress?: string;
  guestCountry?: string;
  guestNationality?: string;
  guestDateOfBirth?: string;
  guestPassportNumber?: string;
  
  // Booking details
  ratePlan?: string;
  boardType?: string;
  roomRate?: number;
  taxes?: number;
  fees?: number;
  discounts?: number;
  
  // Provider information
  providerId?: string;
  providerBookingId?: string;
  providerConfirmationNumber?: string;
  
  // Additional metadata
  bookingNotes?: string;
  internalNotes?: string;
  tags?: string[];
  isVip?: boolean;
  loyaltyMember?: boolean;
  repeatGuest?: boolean;
}

// Guest information interface
export interface GuestInfo {
  name: string;
  email: string;
  phone: string;
  address?: string;
  country?: string;
  nationality?: string;
  dateOfBirth?: string;
  passportNumber?: string;
}

// Request interfaces for API operations
export interface CreateBookingRequest {
  hotelId: string;
  roomId: string;
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  checkInDate: string;
  checkOutDate: string;
  adults: number;
  children: number;
  totalAmount: number;
  currency: string;
  bookingSource: BookingSource;
  specialRequests?: string;
  
  // Additional guest information
  guestAddress?: string;
  guestCountry?: string;
  guestNationality?: string;
  guestDateOfBirth?: string;
  guestPassportNumber?: string;
  
  // Booking details
  ratePlan?: string;
  boardType?: string;
  roomRate?: number;
  taxes?: number;
  fees?: number;
  discounts?: number;
  
  // Provider information
  providerId?: string;
  providerBookingId?: string;
  
  // Additional metadata
  bookingNotes?: string;
  tags?: string[];
  isVip?: boolean;
  loyaltyMember?: boolean;
}

export interface UpdateBookingRequest {
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  checkInDate?: string;
  checkOutDate?: string;
  adults?: number;
  children?: number;
  status?: BookingStatus;
  totalAmount?: number;
  paymentStatus?: PaymentStatus;
  specialRequests?: string;
  
  // Additional guest information
  guestAddress?: string;
  guestCountry?: string;
  guestNationality?: string;
  guestDateOfBirth?: string;
  guestPassportNumber?: string;
  
  // Booking details
  ratePlan?: string;
  boardType?: string;
  roomRate?: number;
  taxes?: number;
  fees?: number;
  discounts?: number;
  
  // Additional metadata
  bookingNotes?: string;
  internalNotes?: string;
  tags?: string[];
  isVip?: boolean;
  loyaltyMember?: boolean;
}

// Filter interface for booking search
export interface BookingSearchFilters {
  search?: string;
  status?: BookingStatus | '';
  paymentStatus?: PaymentStatus | '';
  bookingSource?: BookingSource | '';
  hotelId?: string;
  checkInFrom?: string;
  checkInTo?: string;
  checkOutFrom?: string;
  checkOutTo?: string;
  createdFrom?: string;
  createdTo?: string;
  guestCountry?: string;
  isVip?: boolean;
  loyaltyMember?: boolean;
  repeatGuest?: boolean;
}

// Response interfaces
export interface BookingListResponse {
  data: Booking[];
  total: number;
  page: number;
  limit: number;
}

export interface BookingResponse {
  data: Booking;
  message?: string;
}

// Error response interface
export interface BookingErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

// Booking statistics interface
export interface BookingStats {
  totalBookings: number;
  confirmedBookings: number;
  cancelledBookings: number;
  checkedInBookings: number;
  checkedOutBookings: number;
  noShowBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  occupancyRate: number;
  cancellationRate: number;
  noShowRate: number;
  sourceDistribution: Array<{
    source: BookingSource;
    count: number;
    percentage: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    bookings: number;
    revenue: number;
  }>;
}

// Booking validation rules
export const BookingValidationRules = {
  guestName: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  guestEmail: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  guestPhone: {
    required: true,
    minLength: 10,
    maxLength: 20
  },
  adults: {
    required: true,
    min: 1,
    max: 10
  },
  children: {
    min: 0,
    max: 10
  },
  totalAmount: {
    required: true,
    min: 0
  }
};

// Common currencies
export const BookingCurrencies = [
  'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
  'MXN', 'SGD', 'HKD', 'NOK', 'KRW', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR'
];

// Common countries
export const CommonCountries = [
  'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany',
  'France', 'Italy', 'Spain', 'Netherlands', 'Switzerland', 'Japan',
  'South Korea', 'China', 'Singapore', 'Thailand', 'Malaysia', 'India',
  'Brazil', 'Mexico', 'Argentina', 'South Africa', 'Russia', 'Turkey'
];

// Utility functions for booking data manipulation
export const BookingUtils = {
  // Format booking display name
  formatDisplayName: (booking: Booking): string => {
    return `${booking.bookingReference} - ${booking.guestName}`;
  },

  // Get status badge color
  getStatusColor: (status: BookingStatus): string => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'checked_in':
        return 'bg-blue-100 text-blue-800';
      case 'checked_out':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'no_show':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Get payment status badge color
  getPaymentStatusColor: (paymentStatus: PaymentStatus): string => {
    switch (paymentStatus) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'partial':
        return 'bg-orange-100 text-orange-800';
      case 'refunded':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Calculate nights between dates
  calculateNights: (checkInDate: string, checkOutDate: string): number => {
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const diffTime = checkOut.getTime() - checkIn.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  // Format currency amount
  formatAmount: (amount: number, currency: string): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  },

  // Format date range
  formatDateRange: (checkInDate: string, checkOutDate: string): string => {
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const options: Intl.DateTimeFormatOptions = { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    };
    
    return `${checkIn.toLocaleDateString('en-US', options)} - ${checkOut.toLocaleDateString('en-US', options)}`;
  },

  // Check if booking is upcoming
  isUpcoming: (booking: Booking): boolean => {
    const today = new Date();
    const checkInDate = new Date(booking.checkInDate);
    return checkInDate > today && booking.status === 'confirmed';
  },

  // Check if booking is current
  isCurrent: (booking: Booking): boolean => {
    const today = new Date();
    const checkInDate = new Date(booking.checkInDate);
    const checkOutDate = new Date(booking.checkOutDate);
    return today >= checkInDate && today < checkOutDate && 
           (booking.status === 'confirmed' || booking.status === 'checked_in');
  },

  // Check if booking is past
  isPast: (booking: Booking): boolean => {
    const today = new Date();
    const checkOutDate = new Date(booking.checkOutDate);
    return checkOutDate <= today;
  },

  // Validate booking data
  validateBooking: (booking: Partial<Booking>): string[] => {
    const errors: string[] = [];
    
    if (!booking.guestName || booking.guestName.length < 2) {
      errors.push('Guest name must be at least 2 characters long');
    }
    
    if (!booking.guestEmail || !BookingValidationRules.guestEmail.pattern.test(booking.guestEmail)) {
      errors.push('Valid email address is required');
    }
    
    if (!booking.guestPhone || booking.guestPhone.length < 10) {
      errors.push('Valid phone number is required');
    }
    
    if (!booking.adults || booking.adults < 1) {
      errors.push('At least 1 adult is required');
    }
    
    if (booking.children && booking.children < 0) {
      errors.push('Number of children cannot be negative');
    }
    
    if (!booking.checkInDate) {
      errors.push('Check-in date is required');
    }
    
    if (!booking.checkOutDate) {
      errors.push('Check-out date is required');
    }
    
    if (booking.checkInDate && booking.checkOutDate) {
      const checkIn = new Date(booking.checkInDate);
      const checkOut = new Date(booking.checkOutDate);
      
      if (checkOut <= checkIn) {
        errors.push('Check-out date must be after check-in date');
      }
    }
    
    if (booking.totalAmount !== undefined && booking.totalAmount < 0) {
      errors.push('Total amount cannot be negative');
    }
    
    return errors;
  },

  // Calculate booking revenue
  calculateRevenue: (bookings: Booking[]): number => {
    return bookings
      .filter(booking => booking.status !== 'cancelled')
      .reduce((total, booking) => total + booking.totalAmount, 0);
  },

  // Get bookings by status
  getBookingsByStatus: (bookings: Booking[], status: BookingStatus): Booking[] => {
    return bookings.filter(booking => booking.status === status);
  },

  // Calculate occupancy rate
  calculateOccupancyRate: (bookings: Booking[], totalRooms: number, period: { start: string; end: string }): number => {
    const startDate = new Date(period.start);
    const endDate = new Date(period.end);
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const occupiedRoomNights = bookings
      .filter(booking => booking.status !== 'cancelled')
      .reduce((total, booking) => {
        const checkIn = new Date(booking.checkInDate);
        const checkOut = new Date(booking.checkOutDate);
        
        // Calculate overlap with the period
        const overlapStart = new Date(Math.max(checkIn.getTime(), startDate.getTime()));
        const overlapEnd = new Date(Math.min(checkOut.getTime(), endDate.getTime()));
        
        if (overlapStart < overlapEnd) {
          const overlapDays = Math.ceil((overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60 * 60 * 24));
          return total + overlapDays;
        }
        
        return total;
      }, 0);
    
    const totalAvailableRoomNights = totalRooms * totalDays;
    return totalAvailableRoomNights > 0 ? (occupiedRoomNights / totalAvailableRoomNights) * 100 : 0;
  }
};
