'use client';

import { useState, useEffect } from 'react';
import { ProviderConfig } from '../../../nm.model';

interface ProviderConfigFormProps {
  config?: ProviderConfig | null;
  onSave: (configData: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

export default function ProviderConfigForm({
  config,
  onSave,
  onCancel,
  loading = false
}: ProviderConfigFormProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState({
    company_id: 101,
    channel: 'email',
    provider_type: 'smtp',
    display_name: '',
    config: {},
    priority: 1,
    is_active: true,
    retry_limit: 3,
    rate_limit: 100,
    retry_count: 0,
    retry_delay: 5000,
    timeout_seconds: 30,
    circuit_breaker_threshold: 5,
    circuit_breaker_timeout: 60,
    health_check_url: '',
    health_check_interval: 300,
    last_health_check: new Date().toISOString(),
    health_status: 'unknown',
    cost_per_message: 0.001,
    monthly_quota: 10000,
    current_usage: 0,
    usage_reset_date: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
    tags: {},
    metadata: {}
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Helper function to format JSON for display
  const formatJSON = (obj: any) => {
    if (!obj || Object.keys(obj).length === 0) {
      return '{}';
    }
    return JSON.stringify(obj, null, 2);
  };

  // Separate state for JSON text editing
  const [tagsText, setTagsText] = useState(() => formatJSON(formData.tags));
  const [metadataText, setMetadataText] = useState(() => formatJSON(formData.metadata));
  const [configText, setConfigText] = useState(() => formatJSON(formData.config));

  // Update JSON text when editing existing config (only once on mount)
  useEffect(() => {
    if (config) {
      setTagsText(formatJSON(config.tags));
      setMetadataText(formatJSON(config.metadata));
      setConfigText(formatJSON(config.config));
    }
  }, [config?.id]); // Only run when config ID changes (i.e., different config loaded)

  // Helper function to check if JSON is valid
  const isValidJSON = (text: string): boolean => {
    try {
      JSON.parse(text);
      return true;
    } catch {
      return false;
    }
  };

  // Channel options (matching backend structure)
  const channelOptions = [
    { value: 'email', label: 'Email', icon: 'ri-mail-line' },
    { value: 'sms', label: 'SMS', icon: 'ri-message-2-line' },
    { value: 'whatsapp', label: 'WhatsApp', icon: 'ri-whatsapp-line' },
    { value: 'push', label: 'Push', icon: 'ri-notification-line' }
  ];

  // Provider options (matching backend structure)
  const providerOptions = [
    { value: '', label: 'Default Provider' },
    { value: 'smtp', label: 'SMTP' },
    { value: 'msg91', label: 'MSG91' },
    { value: 'whatsapp', label: 'WhatsApp' },
    { value: 'fcm', label: 'FCM' }
  ];

  useEffect(() => {
    if (config) {
      setFormData({
        company_id: config.company_id,
        channel: config.channel,
        provider_type: config.provider_type,
        display_name: config.display_name,
        config: config.config,
        priority: config.priority,
        is_active: config.is_active || false,
        retry_limit: config.retry_limit || 3,
        rate_limit: config.rate_limit || 100,
        retry_count: config.retry_count || 0,
        retry_delay: config.retry_delay || 5000,
        timeout_seconds: config.timeout_seconds || 30,
        circuit_breaker_threshold: config.circuit_breaker_threshold || 5,
        circuit_breaker_timeout: config.circuit_breaker_timeout || 60,
        health_check_url: config.health_check_url || '',
        health_check_interval: config.health_check_interval || 300,
        last_health_check: config.last_health_check || new Date().toISOString(),
        health_status: config.health_status || 'unknown',
        cost_per_message: config.cost_per_message || 0.001,
        monthly_quota: config.monthly_quota || 10000,
        current_usage: config.current_usage || 0,
        usage_reset_date: config.usage_reset_date || new Date().toISOString().split('T')[0],
        tags: config.tags || {},
        metadata: config.metadata || {}
      });
    }
  }, [config]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (formData.display_name && formData.health_check_url) {
        setIsAutoSaving(true);
        setTimeout(() => setIsAutoSaving(false), 1000);
      }
    }, 3000);

    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  // Helper function to sanitize input values
  const sanitizeInput = (value: string): string => {
    if (typeof value !== 'string') return value;
    // Remove potentially dangerous characters and trim whitespace
    return value.replace(/[<>]/g, '').trim();
  };

  const handleInputChange = (field: string, value: any) => {
    // Sanitize string inputs
    const sanitizedValue = typeof value === 'string' ? sanitizeInput(value) : value;

    setFormData(prev => ({ ...prev, [field]: sanitizedValue }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleConfigChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config: { ...prev.config, [key]: value }
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required field validation
    if (!formData.display_name.trim()) newErrors.display_name = 'Display name is required';
    if (!formData.channel) newErrors.channel = 'Channel is required';
    if (!formData.provider_type) newErrors.provider_type = 'Provider type is required';
    if (!formData.health_check_url.trim()) newErrors.health_check_url = 'Health check URL is required';
    if (!formData.company_id) newErrors.company_id = 'Company ID is required';

    // Length validation
    if (formData.display_name.length > 100) newErrors.display_name = 'Display name must be less than 100 characters';
    if (formData.health_check_url.length > 500) newErrors.health_check_url = 'Health check URL must be less than 500 characters';

    // Numeric validation
    if (formData.company_id < 1) newErrors.company_id = 'Company ID must be a positive number';
    if (formData.priority < 1 || formData.priority > 100) newErrors.priority = 'Priority must be between 1 and 100';
    if (formData.cost_per_message < 0) newErrors.cost_per_message = 'Cost per message cannot be negative';
    if (formData.monthly_quota < 1) newErrors.monthly_quota = 'Monthly quota must be at least 1';

    // Range validation for technical settings
    if (formData.retry_limit < 0 || formData.retry_limit > 10) newErrors.retry_limit = 'Retry limit must be between 0 and 10';
    if (formData.rate_limit < 1 || formData.rate_limit > 10000) newErrors.rate_limit = 'Rate limit must be between 1 and 10,000';
    if (formData.retry_delay < 1000 || formData.retry_delay > 300000) newErrors.retry_delay = 'Retry delay must be between 1,000ms and 300,000ms';
    if (formData.timeout_seconds < 5 || formData.timeout_seconds > 300) newErrors.timeout_seconds = 'Timeout must be between 5 and 300 seconds';

    // URL validation
    if (formData.health_check_url && !isValidUrl(formData.health_check_url)) {
      newErrors.health_check_url = 'Please enter a valid URL (must start with http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper function for URL validation
  const isValidUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  };

  // Helper function to get field validation state
  const getFieldValidationClass = (fieldName: string, value: any): string => {
    const hasError = errors[fieldName];
    const hasValue = value && value.toString().trim();

    if (hasError) {
      return 'border-red-300 focus:ring-red-500 focus:border-red-500';
    } else if (hasValue && !hasError) {
      return 'border-green-300 focus:ring-green-500 focus:border-green-500';
    }
    return 'border-gray-300 focus:ring-blue-500 focus:border-transparent';
  };

  // Helper function to show field validation icon
  const getFieldValidationIcon = (fieldName: string, value: any) => {
    const hasError = errors[fieldName];
    const hasValue = value && value.toString().trim();

    if (hasError) {
      return <i className="ri-error-warning-line text-red-500 text-sm"></i>;
    } else if (hasValue && !hasError) {
      return <i className="ri-check-line text-green-500 text-sm"></i>;
    }
    return null;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return; // Prevent submission during loading
    if (validateForm()) {
      onSave(formData);
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: 'ri-information-line' },
    { id: 'configuration', label: 'Configuration', icon: 'ri-settings-line' },
    { id: 'limits', label: 'Limits & Quotas', icon: 'ri-bar-chart-line' },
    { id: 'advanced', label: 'Advanced', icon: 'ri-tools-line' }
  ];

  const renderProviderConfigFields = () => {
    switch (formData.provider_type) {
      case 'sendgrid':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">API Key *</label>
              <input
                type="password"
                value={(formData.config as any)?.api_key || ''}
                onChange={(e) => handleConfigChange('api_key', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="SG.*********************"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">From Email *</label>
              <input
                type="email"
                value={(formData.config as any)?.from_email || ''}
                onChange={(e) => handleConfigChange('from_email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">From Name</label>
              <input
                type="text"
                value={(formData.config as any)?.from_name || ''}
                onChange={(e) => handleConfigChange('from_name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Company Name"
              />
            </div>
          </>
        );
      case 'twilio':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Account SID *</label>
              <input
                type="text"
                value={(formData.config as any)?.account_sid || ''}
                onChange={(e) => handleConfigChange('account_sid', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="AC*********************"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Auth Token *</label>
              <input
                type="password"
                value={(formData.config as any)?.auth_token || ''}
                onChange={(e) => handleConfigChange('auth_token', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="*********************"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">From Number *</label>
              <input
                type="tel"
                value={(formData.config as any)?.from_number || ''}
                onChange={(e) => handleConfigChange('from_number', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="+1234567890"
              />
            </div>
          </>
        );
      default:
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Configuration JSON</label>
            <textarea
              value={configText}
              onChange={(e) => {
                const newValue = e.target.value;
                setConfigText(newValue);

                // Only update form data if JSON is valid
                try {
                  const parsed = JSON.parse(newValue);
                  handleInputChange('config', parsed);
                } catch {
                  // Invalid JSON - don't update form data, just keep the text
                }
              }}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent font-mono text-sm ${
                isValidJSON(configText) ? 'border-gray-300' : 'border-red-300 bg-red-50'
              }`}
              rows={6}
              placeholder='{"host": "smtp.gmail.com", "port": 587, "username": "<EMAIL>", "password": "password"}'
            />
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-gray-500">
                JSON object for provider configuration
              </p>
              {isValidJSON(configText) ? (
                <p className="text-xs text-green-600 flex items-center">
                  <i className="ri-check-line mr-1"></i>
                  Valid JSON
                </p>
              ) : (
                <p className="text-xs text-red-600 flex items-center">
                  <i className="ri-error-warning-line mr-1"></i>
                  Invalid JSON
                </p>
              )}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-1 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  relative px-4 py-3 font-medium text-sm transition-all duration-200 
                  whitespace-nowrap border-b-2 min-w-max
                  ${activeTab === tab.id
                    ? 'text-blue-600 border-blue-600 bg-blue-50/50'
                    : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <div className="flex items-center">
                  <i className={`${tab.icon} mr-2 text-base`}></i>
                  {tab.label}
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Auto-save indicator */}
      {isAutoSaving && (
        <div className="flex-shrink-0 bg-green-50 border-b border-green-200 px-6 py-2">
          <div className="flex items-center text-sm text-green-700">
            <i className="ri-save-line mr-2 animate-pulse"></i>
            Auto-saving...
          </div>
        </div>
      )}

      {/* Form Content */}
      <form id="provider-config-form" onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar">
        <div className="p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-information-line mr-3 text-blue-600"></i>
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company ID *
                    </label>
                    <input
                      type="number"
                      value={formData.company_id}
                      onChange={(e) => handleInputChange('company_id', parseInt(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.company_id ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter company ID"
                      required
                    />
                    {errors.company_id && (
                      <p className="text-red-500 text-xs mt-1">{errors.company_id}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Display Name *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.display_name}
                        onChange={(e) => handleInputChange('display_name', e.target.value)}
                        className={`w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 ${getFieldValidationClass('display_name', formData.display_name)}`}
                        placeholder="e.g., SendGrid Email Service"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        {getFieldValidationIcon('display_name', formData.display_name)}
                      </div>
                    </div>
                    {errors.display_name && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <i className="ri-error-warning-line mr-1"></i>
                        {errors.display_name}
                      </p>
                    )}
                    {!errors.display_name && formData.display_name.trim() && (
                      <p className="text-green-600 text-xs mt-1 flex items-center">
                        <i className="ri-check-line mr-1"></i>
                        Valid display name
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Channel *
                    </label>
                    <select
                      value={formData.channel}
                      onChange={(e) => handleInputChange('channel', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.channel ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      {channelOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.channel && (
                      <p className="text-red-500 text-xs mt-1">{errors.channel}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Provider Type *
                    </label>
                    <select
                      value={formData.provider_type}
                      onChange={(e) => handleInputChange('provider_type', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.provider_type ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      <option value="">Select provider type</option>
                      {providerOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {errors.provider_type && (
                      <p className="text-red-500 text-xs mt-1">{errors.provider_type}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority *
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.priority}
                      onChange={(e) => handleInputChange('priority', parseInt(e.target.value) || 1)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.priority ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="1"
                      required
                    />
                    {errors.priority && (
                      <p className="text-red-500 text-xs mt-1">{errors.priority}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      Lower numbers have higher priority
                    </p>
                  </div>

                  <div className="md:col-span-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="is_active"
                        checked={formData.is_active}
                        onChange={(e) => handleInputChange('is_active', e.target.checked)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                        Configuration is active
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Inactive configurations will not be used for sending notifications
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Configuration Tab */}
          {activeTab === 'configuration' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-6 border border-green-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-settings-line mr-3 text-green-600"></i>
                  Provider Configuration
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Health Check URL *
                    </label>
                    <input
                      type="url"
                      value={formData.health_check_url}
                      onChange={(e) => handleInputChange('health_check_url', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                        errors.health_check_url ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="https://api.provider.com/health"
                      required
                    />
                    {errors.health_check_url && (
                      <p className="text-red-500 text-xs mt-1">{errors.health_check_url}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Health Check Interval (seconds)
                      </label>
                      <input
                        type="number"
                        min="60"
                        value={formData.health_check_interval}
                        onChange={(e) => handleInputChange('health_check_interval', parseInt(e.target.value) || 300)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="300"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Timeout (seconds)
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={formData.timeout_seconds}
                        onChange={(e) => handleInputChange('timeout_seconds', parseInt(e.target.value) || 30)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="30"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Health Status
                      </label>
                      <select
                        value={formData.health_status}
                        onChange={(e) => handleInputChange('health_status', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      >
                        <option value="unknown">Unknown</option>
                        <option value="healthy">Healthy</option>
                        <option value="unhealthy">Unhealthy</option>
                        <option value="degraded">Degraded</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Health Check
                      </label>
                      <input
                        type="datetime-local"
                        value={formData.last_health_check ? new Date(formData.last_health_check).toISOString().slice(0, 16) : ''}
                        onChange={(e) => handleInputChange('last_health_check', e.target.value ? new Date(e.target.value).toISOString() : '')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Last time health check was performed
                      </p>
                    </div>
                  </div>

                  {/* Provider-specific configuration */}
                  <div className="bg-white rounded-lg p-4 border border-green-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">
                      {formData.provider_type.charAt(0).toUpperCase() + formData.provider_type.slice(1)} Configuration
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {renderProviderConfigFields()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Limits & Quotas Tab */}
          {activeTab === 'limits' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-bar-chart-line mr-3 text-purple-600"></i>
                  Limits & Quotas
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rate Limit (messages/minute)
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.rate_limit}
                      onChange={(e) => handleInputChange('rate_limit', parseInt(e.target.value) || 100)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Monthly Quota
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.monthly_quota}
                      onChange={(e) => handleInputChange('monthly_quota', parseInt(e.target.value) || 10000)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="10000"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cost per Message (USD)
                    </label>
                    <input
                      type="number"
                      step="0.0001"
                      min="0"
                      value={formData.cost_per_message}
                      onChange={(e) => handleInputChange('cost_per_message', parseFloat(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                        errors.cost_per_message ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="0.001"
                    />
                    {errors.cost_per_message && (
                      <p className="text-red-500 text-xs mt-1">{errors.cost_per_message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Retry Limit
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={formData.retry_limit}
                      onChange={(e) => handleInputChange('retry_limit', parseInt(e.target.value) || 3)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="3"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Retry Delay (milliseconds)
                    </label>
                    <input
                      type="number"
                      min="1000"
                      value={formData.retry_delay}
                      onChange={(e) => handleInputChange('retry_delay', parseInt(e.target.value) || 5000)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="5000"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Retry Count
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={formData.retry_count}
                      onChange={(e) => handleInputChange('retry_count', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Current retry count (read-only in practice)
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Usage
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={formData.current_usage}
                      onChange={(e) => handleInputChange('current_usage', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Current month's message count
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Usage Reset Date
                    </label>
                    <input
                      type="date"
                      value={formData.usage_reset_date}
                      onChange={(e) => handleInputChange('usage_reset_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Date when usage counter resets (YYYY-MM-DD)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <i className="ri-tools-line mr-3 text-orange-600"></i>
                  Advanced Settings
                </h3>

                <div className="space-y-6">
                  {/* Circuit Breaker Settings */}
                  <div className="bg-white rounded-lg p-4 border border-orange-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Circuit Breaker</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Failure Threshold
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={formData.circuit_breaker_threshold}
                          onChange={(e) => handleInputChange('circuit_breaker_threshold', parseInt(e.target.value) || 5)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="5"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Number of failures before circuit opens
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Timeout (seconds)
                        </label>
                        <input
                          type="number"
                          min="30"
                          value={formData.circuit_breaker_timeout}
                          onChange={(e) => handleInputChange('circuit_breaker_timeout', parseInt(e.target.value) || 60)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="60"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Time before attempting to close circuit
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Tags and Metadata */}
                  <div className="bg-white rounded-lg p-4 border border-orange-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Tags & Metadata</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Tags (JSON)
                        </label>
                        <textarea
                          value={tagsText}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            setTagsText(newValue);

                            // Only update form data if JSON is valid
                            try {
                              const parsed = JSON.parse(newValue);
                              handleInputChange('tags', parsed);
                            } catch {
                              // Invalid JSON - don't update form data, just keep the text
                            }
                          }}
                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent font-mono text-sm ${
                            isValidJSON(tagsText) ? 'border-gray-300' : 'border-red-300 bg-red-50'
                          }`}
                          rows={4}
                          placeholder='{"environment": "production", "region": "us-east-1"}'
                        />
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-500">
                            JSON object for tagging (e.g., environment, region)
                          </p>
                          {isValidJSON(tagsText) ? (
                            <p className="text-xs text-green-600 flex items-center">
                              <i className="ri-check-line mr-1"></i>
                              Valid JSON
                            </p>
                          ) : (
                            <p className="text-xs text-red-600 flex items-center">
                              <i className="ri-error-warning-line mr-1"></i>
                              Invalid JSON
                            </p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Metadata (JSON)
                        </label>
                        <textarea
                          value={metadataText}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            setMetadataText(newValue);

                            // Only update form data if JSON is valid
                            try {
                              const parsed = JSON.parse(newValue);
                              handleInputChange('metadata', parsed);
                            } catch {
                              // Invalid JSON - don't update form data, just keep the text
                            }
                          }}
                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent font-mono text-sm ${
                            isValidJSON(metadataText) ? 'border-gray-300' : 'border-red-300 bg-red-50'
                          }`}
                          rows={4}
                          placeholder='{"notes": "Production provider", "created_by": "admin"}'
                        />
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-500">
                            JSON object for additional metadata
                          </p>
                          {isValidJSON(metadataText) ? (
                            <p className="text-xs text-green-600 flex items-center">
                              <i className="ri-check-line mr-1"></i>
                              Valid JSON
                            </p>
                          ) : (
                            <p className="text-xs text-red-600 flex items-center">
                              <i className="ri-error-warning-line mr-1"></i>
                              Invalid JSON
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
