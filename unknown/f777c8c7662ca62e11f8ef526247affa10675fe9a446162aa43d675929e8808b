'use client';

import { useState } from 'react';
import { ProviderConfig } from '../../../nm.model';

interface ProviderConfigsListProps {
  configs: ProviderConfig[];
  onEdit: (config: ProviderConfig) => void;
  onView: (config: ProviderConfig) => void;
  onDelete?: (config: ProviderConfig) => void;
  onCreate: () => void;
  loading?: boolean;
}

export default function ProviderConfigsList({
  configs,
  onEdit,
  onView,
  onDelete,
  onCreate,
  loading = false
}: ProviderConfigsListProps) {

  const getStatusBadge = (isActive?: boolean) => {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
          isActive ? 'bg-green-500' : 'bg-red-500'
        }`}></div>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getHealthStatusBadge = (status?: string) => {
    const statusConfig = {
      healthy: { color: 'bg-green-100 text-green-800', icon: 'ri-check-line' },
      unhealthy: { color: 'bg-red-100 text-red-800', icon: 'ri-close-line' },
      warning: { color: 'bg-yellow-100 text-yellow-800', icon: 'ri-alert-line' },
      unknown: { color: 'bg-gray-100 text-gray-800', icon: 'ri-question-line' }
    };
    
    const statusStyle = statusConfig[status as keyof typeof statusConfig] || statusConfig.unknown;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusStyle.color}`}>
        <i className={`${statusStyle.icon} mr-1`}></i>
        {status || 'Unknown'}
      </span>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'ri-mail-line';
      case 'sms':
        return 'ri-message-2-line';
      case 'whatsapp':
        return 'ri-whatsapp-line';
      case 'push':
        return 'ri-notification-line';
      default:
        return 'ri-send-plane-line';
    }
  };

  const getChannelColor = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'text-blue-600 bg-blue-100';
      case 'sms':
        return 'text-green-600 bg-green-100';
      case 'whatsapp':
        return 'text-green-600 bg-green-100';
      case 'push':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  const getUsagePercentage = (config: ProviderConfig) => {
    if (!config.monthly_quota || !config.current_usage) return 0;
    return Math.round((config.current_usage / config.monthly_quota) * 100);
  };



  return (
    <div className="space-y-6">


      {/* Configurations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {configs.map((config) => (
          <div key={config.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            {/* Card Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${getChannelColor(config.channel)}`}>
                    <i className={`${getChannelIcon(config.channel)} text-xl`}></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {config.display_name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {config.provider_type.toUpperCase()} • Priority {config.priority}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-1">
                  {getStatusBadge(config.is_active)}
                  {getHealthStatusBadge(config.health_status)}
                </div>
              </div>

              {/* Usage Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Usage</span>
                  <span className="font-medium text-gray-900">
                    {config.current_usage?.toLocaleString()} / {config.monthly_quota?.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(getUsagePercentage(config), 100)}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>{getUsagePercentage(config)}% used</span>
                  <span>{formatCurrency(config.cost_per_message)} per message</span>
                </div>
              </div>
            </div>

            {/* Card Actions */}
            <div className="p-4 bg-gray-50 rounded-b-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <i className="ri-time-line"></i>
                  <span>Updated {new Date(config.updated_at || '').toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onView(config)}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="View Configuration"
                  >
                    <i className="ri-eye-line"></i>
                  </button>
                  <button
                    onClick={() => onEdit(config)}
                    className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Edit Configuration"
                  >
                    <i className="ri-edit-line"></i>
                  </button>
                  <button
                    onClick={() => onDelete?.(config)}
                    disabled={loading}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Delete Configuration"
                  >
                    {loading ? (
                      <i className="ri-loader-line animate-spin"></i>
                    ) : (
                      <i className="ri-delete-bin-line"></i>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {configs.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-settings-3-line text-2xl text-gray-400"></i>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No configurations found</h3>
          <p className="text-gray-600 mb-6">
            Get started by creating your first provider configuration
          </p>
          <button
            onClick={onCreate}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i className="ri-add-line mr-2"></i>
            Create Configuration
          </button>
        </div>
      )}
    </div>
  );
}
