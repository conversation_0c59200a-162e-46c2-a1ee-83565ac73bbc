"use client";

import { useCallback, useEffect, useState } from "react";
import NotificationTemplateList from "./components/NotificationTemplatesList";
import NotificationTemplateForm from "./components/NotificationTemplateForm";
import Modal from "@/app/components/ui/Modal";
import TabbedModal from "@/app/components/ui/TabbedModal";
import { NotificationTemplate } from "../../nm.model";
import { getNotificationTemplates, getNotificationTemplatesByID, createNotificationTemplates, updateNotificationTemplates, deleteNotificationTemplates } from "../../nm-service";

// interface NotificationTemplate {
//   id: string; // auto-generated
//   company_id: number; // required
//   event_key: string; // required
//   channel: string; // required
//   language: string; // required
//   subject: string; // required
//   template_id: string; // required
//   provider?: string; // optional
//   content: string; // required
//   is_active: boolean;
//   created_at: string; // ISO date string
//   updated_at: string; // ISO date string
// }

export default function NotificationTemplates() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<NotificationTemplate | null>(null);
  const [isViewMode, setIsViewMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [channelFilter, setChannelFilter] = useState("");

  const [templates, setTemplates] = useState<NotificationTemplate[]>([
    // {
    //   id: "1",
    //   company_id: 101,
    //   event_key: "user_signup",
    //   channel: "email",
    //   language: "en",
    //   subject: "Welcome to our platform!",
    //   template_id: "welcome_email_en",
    //   provider: "sendgrid",
    //   content: "Welcome {{user_name}}, thank you for signing up!",
    //   is_active: true,
    //   created_at: "2024-01-15T10:30:00Z",
    //   updated_at: "2024-01-20T14:45:00Z",
    // },
    // {
    //   id: "2",
    //   company_id: 101,
    //   event_key: "password_reset",
    //   channel: "sms",
    //   language: "en",
    //   subject: "Password Reset Code",
    //   template_id: "password_reset_sms_en",
    //   content: "Your password reset code is: {{reset_code}}",
    //   is_active: false,
    //   created_at: "2024-01-16T09:15:00Z",
    //   updated_at: "2024-01-18T11:20:00Z",
    // },
  ]);

  const [loading, setLoading] = useState(false);
  const [viewLoading, setViewLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null); 
      const res = await getNotificationTemplates();
      //console.log("template fetched result:", res);
      
      setTemplates(res);
    } catch (err) {
      console.error("Error fetching templates:", err);
      setError("Failed to load templates");
    } finally {
      setLoading(false);
    }
  }, []); 

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // Filter templates based on search and filters
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      (template.subject || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.event_key || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.channel || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.language || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.template_id || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && template.is_active) ||
      (statusFilter === "inactive" && !template.is_active);

    const matchesChannel = !channelFilter || template.channel === channelFilter;

    return matchesSearch && matchesStatus && matchesChannel;
  });

  // Get unique channels for filter dropdown
  const uniqueChannels = [
    ...new Set(templates.map((template) => template.channel).filter(Boolean)),
  ];

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleEditTemplate = (template: NotificationTemplate) => {
    setSelectedTemplate(template);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleViewTemplate = async (template: NotificationTemplate) => {
    try {
      setIsViewMode(true);
      setIsFormOpen(true);
      setSelectedTemplate(null); // Clear previous data
      setViewLoading(true);

      // Fetch fresh template data using string UUID
      const freshTemplate = await getNotificationTemplatesByID(template.id);
      setSelectedTemplate(freshTemplate);

    } catch (error) {
      console.error("Error fetching template details:", error);
      setError("Failed to load template details");
      // Fallback to existing template data
      setSelectedTemplate(template);
    } finally {
      setViewLoading(false);
    }
  };



  const getActiveCount = () => {
    return filteredTemplates.filter((template) => template.is_active).length;
  };

  const getInactiveCount = () => {
    return filteredTemplates.filter((template) => !template.is_active).length;
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setSelectedTemplate(null);
    setIsViewMode(false);
  };

  const handleDeleteTemplate = async (template: NotificationTemplate) => {
    // Show confirmation dialog
    const isConfirmed = window.confirm(
      `Are you sure you want to delete the template "${template.subject}"?\n\nThis action cannot be undone.`
    );

    if (!isConfirmed) return;

    try {
      setFormLoading(true);
      setError(null);

      // Call delete API
      await deleteNotificationTemplates(template.id);

      // Refresh the list after successful deletion
      await fetchTemplates();
    } catch (error: any) {
      console.error("Error deleting template:", error);
      const errorMessage = error?.response?.data?.message || error?.message || "Failed to delete template";
      setError(`Delete failed: ${errorMessage}`);
    } finally {
      setFormLoading(false);
    }
  };

  // Helper function to convert form data to JSON (not FormData)
  const createRequestBody = (templateData: any) => {
    // Return the data as a proper JSON object with correct types
    return {
      company_id: parseInt(templateData.company_id) || 101,
      event_key: templateData.event_key,
      channel: templateData.channel,
      language: templateData.language,
      subject: templateData.subject,
      template_id: templateData.template_id,
      provider: templateData.provider || '',
      content: templateData.content,
      is_active: Boolean(templateData.is_active)
    };
  };

  const handleFormSave = async (templateData: any) => {
    try {
      setFormLoading(true);
      setError(null);

      if (selectedTemplate) {
        // Update existing template
        const requestBody = createRequestBody(templateData);
        await updateNotificationTemplates(selectedTemplate.id, requestBody);

        // Refresh the entire list after successful update
        await fetchTemplates();
      } else {
        // Create new template
        const requestBody = createRequestBody(templateData);
        await createNotificationTemplates(requestBody);

        // Refresh the entire list after successful creation
        await fetchTemplates();
      }

      handleFormClose();
    } catch (error: any) {
      console.error("Error saving template:", error);
      const errorMessage = error?.response?.data?.message || error?.message || "Operation failed";
      setError(selectedTemplate ? `Update failed: ${errorMessage}` : `Create failed: ${errorMessage}`);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Notification Templates
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage notification templates for different events and channels
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <div className="bg-blue-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-blue-700">
                Total Templates: {filteredTemplates.length}
              </span>
            </div>
            <div className="bg-green-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-green-700">
                Active: {getActiveCount()}
              </span>
            </div>
            <div className="bg-red-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-red-700">
                Inactive: {getInactiveCount()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive Actions Bar */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <button
            onClick={handleCreateTemplate}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 sm:py-3 rounded-lg font-medium shadow-sm transition-colors whitespace-nowrap"
          >
            <i className="ri-add-line mr-2"></i>
            Add Template
          </button>
        </div>

        {/* Responsive Filters */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search Templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto min-w-48"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) =>
              setStatusFilter(e.target.value as "all" | "active" | "inactive")
            }
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>

          <select
            value={channelFilter}
            onChange={(e) => setChannelFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Channels</option>
            {uniqueChannels.map((channel) => (
              <option key={channel} value={channel}>
                {channel.charAt(0).toUpperCase() + channel.slice(1)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Responsive Template List */}
      <div>
        <NotificationTemplateList
          templates={filteredTemplates}
          onEdit={handleEditTemplate}
          onView={handleViewTemplate}
          onDelete={handleDeleteTemplate}
          loading={formLoading}
        />
      </div>

      {/* Template View Modal */}
      {isFormOpen && isViewMode && (
        <TemplateViewTabbedModal
          template={selectedTemplate}
          isOpen={isFormOpen && isViewMode}
          onClose={handleFormClose}
          loading={viewLoading}
        />
      )}

      {/* Template Form Modal */}
      {isFormOpen && !isViewMode && (
        <Modal
          isOpen={isFormOpen && !isViewMode}
          onClose={handleFormClose}
          title={
            selectedTemplate
              ? "Edit Notification Template"
              : "Create Notification Template"
          }
          subtitle={
            selectedTemplate
              ? `Editing template: ${selectedTemplate.template_id}`
              : "Create a new notification template"
          }
          size="xl"
          height="fixed"
          footer={
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-500">
                {formLoading ? (
                  <>
                    <i className="ri-loader-line mr-1 animate-spin"></i>
                    {selectedTemplate ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-1"></i>
                    Auto-saved
                  </>
                )}
              </div>
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={handleFormClose}
                  disabled={formLoading}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  form="template-form"
                  disabled={formLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {formLoading && <i className="ri-loader-line mr-2 animate-spin"></i>}
                  {selectedTemplate ? "Update Template" : "Create Template"}
                </button>
              </div>
            </div>
          }
        >
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <i className="ri-error-warning-line text-red-600 mr-2"></i>
                <span className="text-red-700 text-sm">{error}</span>
                <button
                  onClick={() => setError(null)}
                  className="ml-auto text-red-600 hover:text-red-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </div>
            </div>
          )}
          <NotificationTemplateForm
            template={selectedTemplate}
            onSave={handleFormSave}
            onCancel={handleFormClose}
            loading={formLoading}
          />
        </Modal>
      )}
    </div>
  );
}

// Template View Modal Component (inline like HotelMaster)
function TemplateViewTabbedModal({
  template,
  isOpen,
  onClose,
  loading,
}: {
  template: NotificationTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  loading: boolean;
}) {
  // Show loading state
  if (loading || !template) {
    return (
      <TabbedModal
        isOpen={isOpen}
        onClose={onClose}
        title="Loading Template..."
        subtitle="Please wait while we fetch the template details"
        tabs={[
          {
            id: "loading",
            label: "Loading",
            icon: "ri-loader-line",
            content: (
              <div className="p-12 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-loader-line text-2xl text-blue-600 animate-spin"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Template</h3>
                <p className="text-gray-600">Fetching the latest template data...</p>
              </div>
            )
          }
        ]}
        size="full"
        height="fixed"
      />
    );
  }

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
          isActive
            ? "bg-green-100 text-green-800 border border-green-200"
            : "bg-red-100 text-red-800 border border-red-200"
        }`}
      >
        <div
          className={`w-2 h-2 rounded-full mr-2 ${
            isActive ? "bg-green-500" : "bg-red-500"
          }`}
        ></div>
        {isActive ? "Active" : "Inactive"}
      </span>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch ((channel || '').toLowerCase()) {
      case "email":
        return "ri-mail-line";
      case "sms":
        return "ri-message-2-line";
      case "push":
        return "ri-notification-line";
      case "whatsapp":
        return "ri-whatsapp-line";
      case "fcm":
        return "ri-notification-line";
      default:
        return "ri-send-plane-line";
    }
  };

  const getChannelColor = (channel: string) => {
    switch ((channel || '').toLowerCase()) {
      case "email":
        return "text-blue-600 bg-blue-100";
      case "sms":
        return "text-green-600 bg-green-100";
      case "push":
        return "text-purple-600 bg-purple-100";
      case "whatsapp":
        return "text-green-600 bg-green-100";
      case "fcm":
        return "text-purple-600 bg-purple-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Extract template variables from content
  const extractVariables = (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;
    while ((match = variableRegex.exec(content)) !== null) {
      if (!variables.includes(match[1].trim())) {
        variables.push(match[1].trim());
      }
    }
    return variables;
  };

  const templateVariables = extractVariables(template.content);

  // Dynamic sample data based on template context
  const getSampleData = (eventKey: string, channel: string): Record<string, string> => {
    // Base common data
    const baseData = {
      user_name: "John Doe",
      username: "johndoe",
      email: "<EMAIL>",
      phone: "+1234567890",
      company_name: "Kindali Travel",
      app_name: "Qwermenu",
    };

    // Event-specific data
    const eventData: Record<string, Record<string, string>> = {
      user_signup: {
        verification_code: "123456",
        verification_link: "https://app.example.com/verify/abc123",
        welcome_message: "Welcome to our platform!",
      },
      password_reset: {
        reset_code: "789012",
        reset_link: "https://app.example.com/reset/xyz789",
        expiry_time: "10 minutes",
      },
      booking_confirmation: {
        booking_id: "BK-2024-001",
        hotel_name: "Grand Plaza Hotel",
        check_in_date: "2024-02-15",
        check_out_date: "2024-02-18",
        room_type: "Deluxe Suite",
        total_amount: "$299.99",
      },
      payment_success: {
        transaction_id: "TXN-789456",
        amount: "$149.99",
        payment_method: "Credit Card",
        receipt_url: "https://app.example.com/receipt/123",
      },
      whatsapp_otp: {
        otp: "567890",
        validity: "10 mins",
        service_name: "Qwermenu",
      },
      user_notification: {
        message: "Your order has been confirmed",
        notification_type: "Order Update",
        action_url: "https://app.example.com/orders/123",
      },
    };

    // Channel-specific data
    const channelData: Record<string, Record<string, string>> = {
      email: {
        unsubscribe_link: "https://app.example.com/unsubscribe",
        support_email: "<EMAIL>",
      },
      sms: {
        short_url: "bit.ly/abc123",
        stop_keyword: "STOP",
      },
      whatsapp: {
        business_name: "Qwermenu Support",
        contact_number: "+1-800-SUPPORT",
      },
      websocket: {
        session_id: "sess_abc123",
        timestamp: new Date().toLocaleString(),
      },
    };

    return {
      ...baseData,
      ...(eventData[eventKey] || {}),
      ...(channelData[channel] || {}),
    };
  };

  const sampleData = getSampleData(template.event_key, template.channel);

  const renderPreviewContent = (content: string) => {
    let previewContent = content;
    Object.entries(sampleData).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, "g");
      // Escape HTML to prevent XSS
      const escapedValue = value.replace(/[&<>"']/g, (match) => {
        const escapeMap: Record<string, string> = {
          '&': '&amp;',
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;'
        };
        return escapeMap[match];
      });
      previewContent = previewContent.replace(
        regex,
        `<span class="bg-yellow-100 text-yellow-800 px-1 rounded font-medium">${escapedValue}</span>`
      );
    });
    return previewContent;
  };

  const tabs = [
    {
      id: "overview",
      label: "Overview",
      icon: "ri-information-line",
      content: (
        <div className="p-6 space-y-6">
          {/* Template Header */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div
                  className={`w-16 h-16 rounded-2xl flex items-center justify-center ${getChannelColor(
                    template.channel
                  )}`}
                >
                  <i
                    className={`${getChannelIcon(template.channel)} text-2xl`}
                  ></i>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">
                    {template.subject}
                  </h3>
                  <p className="text-gray-600 mt-1">
                    Template ID: {template.template_id}
                  </p>
                </div>
              </div>
              {getStatusBadge(template.is_active)}
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Event Key</div>
                <div className="font-semibold text-gray-900">
                  {template.event_key}
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Channel</div>
                <div className="font-semibold text-gray-900 capitalize">
                  {template.channel}
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Language</div>
                <div className="font-semibold text-gray-900 uppercase">
                  {template.language}
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Company ID</div>
                <div className="font-semibold text-gray-900">
                  {template.company_id}
                </div>
              </div>
            </div>
          </div>

          {/* Template Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-time-line text-green-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Timeline
                </h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Created</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatDate(template.created_at)}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Last Updated</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatDate(template.updated_at)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-settings-3-line text-purple-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Configuration
                </h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Provider</span>
                  <span className="text-sm font-medium text-gray-900">
                    {template.provider || "Default"}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Variables</span>
                  <span className="text-sm font-medium text-gray-900">
                    {templateVariables.length} variables
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "content",
      label: "Content",
      icon: "ri-file-text-line",
      content: (
        <div className="p-6 space-y-6">
          {/* Subject */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-text text-blue-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Subject Line
              </h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <p className="text-gray-900 font-medium">{template.subject}</p>
            </div>
          </div>

          {/* Template Content */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-code-line text-green-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Template Content
              </h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <pre className="text-sm text-gray-900 whitespace-pre-wrap font-mono">
                {template.content}
              </pre>
            </div>
          </div>

          {/* Variables */}
          {templateVariables.length > 0 && (
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-braces-line text-amber-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Template Variables
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {templateVariables.map((variable, index) => (
                  <div
                    key={index}
                    className="bg-amber-50 rounded-lg p-3 border border-amber-200"
                  >
                    <code className="text-sm font-mono text-amber-800">
                      {`{{${variable}}}`}
                    </code>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      id: "preview",
      label: "Preview",
      icon: "ri-eye-line",
      content: (
        <div className="p-6 space-y-6">
          {/* Preview Header */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center">
                <i className="ri-eye-line text-green-600 text-2xl"></i>
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  Template Preview
                </h3>
                <p className="text-gray-600 mt-1">Preview with sample data</p>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-green-200">
              <div className="text-sm text-green-700 font-medium mb-2">
                Sample Data Used:
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                {Object.entries(sampleData).map(([key, value]) => (
                  <div key={key} className="bg-green-50 rounded px-2 py-1">
                    <span className="font-mono text-green-800">{key}:</span>
                    <span className="text-green-700 ml-1">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Subject Preview */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-mail-line text-blue-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Subject Preview
              </h3>
            </div>
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div
                className="text-gray-900 font-medium"
                dangerouslySetInnerHTML={{
                  __html: renderPreviewContent(template.subject),
                }}
              />
            </div>
          </div>

          {/* Content Preview */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-file-text-line text-purple-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Content Preview
              </h3>
            </div>
            <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <div
                className="text-gray-900 whitespace-pre-wrap"
                dangerouslySetInnerHTML={{
                  __html: renderPreviewContent(template.content),
                }}
              />
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${template.subject}`}
      subtitle={`${
        template.event_key
      } • ${template.channel.toUpperCase()} • ${template.language.toUpperCase()}`}
      tabs={tabs}
      defaultTab="overview"
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-2">
          {getStatusBadge(template.is_active)}
          <div
            className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getChannelColor(
              template.channel
            )}`}
          >
            <i className={`${getChannelIcon(template.channel)} mr-2`}></i>
            {template.channel.toUpperCase()}
          </div>
        </div>
      }
    />
  );
}
