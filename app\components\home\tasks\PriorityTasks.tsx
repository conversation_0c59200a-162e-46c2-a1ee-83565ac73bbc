'use client';

import React from 'react';

export default function PriorityTasks() {
  const priorityTasks = [
    {
      title: 'Critical: Payment Disputes',
      description: '5 high-value disputes require immediate attention',
      dueTime: 'Due in 1 hour',
      priority: 'high',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      dotColor: 'bg-red-500',
      textColor: 'text-red-600'
    },
    {
      title: 'Hotel Inventory Sync',
      description: 'Update 47 properties with new availability',
      dueTime: 'Due today',
      priority: 'medium',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      dotColor: 'bg-amber-500',
      textColor: 'text-amber-600'
    },
    {
      title: 'New Partner Approvals',
      description: 'Review and approve 8 new hotel partnerships',
      dueTime: 'Due tomorrow',
      priority: 'low',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      dotColor: 'bg-blue-500',
      textColor: 'text-blue-600'
    }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <h3 className="font-semibold text-slate-900 text-lg">Priority Tasks</h3>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap">
          Manage All
        </button>
      </div>

      <div className="space-y-3 sm:space-y-4">
        {priorityTasks.map((task, index) => (
          <div
            key={index}
            className={`flex items-start space-x-3 p-3 rounded-lg border ${task.bgColor} ${task.borderColor} overflow-hidden`}
          >
            <div className={`w-2 h-2 ${task.dotColor} rounded-full mt-2 flex-shrink-0`}></div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-slate-900 truncate">{task.title}</p>
              <p className="text-xs text-slate-600 mb-2 line-clamp-2">{task.description}</p>
              <p className={`text-xs font-medium ${task.textColor}`}>{task.dueTime}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
