// Markup status types
export type MarkupStatus = 'active' | 'inactive';

// Markup types
export type MarkupType = 'percentage' | 'rate';

// Provider types
export type ProviderType = 'common markup' | 'provider' | 'hotel';

// Main Markup interface
export interface Markup {
  id: string;
  name: string;
  providerType: ProviderType;
  status: MarkupStatus;
  type: MarkupType;
  value: number;
  description?: string;
  applicableRegions: string[];
  applicableHotels: string[];
  applicableProviders: string[];
  validFrom: string;
  validTo?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  lastModifiedBy: string;
  priority: number;
  
  // Additional configuration fields
  currency?: string;
  minimumAmount?: number;
  maximumAmount?: number;
  conditions?: MarkupCondition[];
  tags?: string[];
  isDefault?: boolean;
  autoApply?: boolean;
}

// Markup condition interface
export interface MarkupCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in';
  value: any;
  description?: string;
}

// Request interfaces for API operations
export interface CreateMarkupRequest {
  name: string;
  providerType: ProviderType;
  status: MarkupStatus;
  type: MarkupType;
  value: number;
  description?: string;
  applicableRegions: string[];
  applicableHotels: string[];
  applicableProviders: string[];
  validFrom: string;
  validTo?: string;
  createdBy: string;
  priority: number;
  
  // Additional configuration fields
  currency?: string;
  minimumAmount?: number;
  maximumAmount?: number;
  conditions?: MarkupCondition[];
  tags?: string[];
  isDefault?: boolean;
  autoApply?: boolean;
}

export interface UpdateMarkupRequest {
  name?: string;
  providerType?: ProviderType;
  status?: MarkupStatus;
  type?: MarkupType;
  value?: number;
  description?: string;
  applicableRegions?: string[];
  applicableHotels?: string[];
  applicableProviders?: string[];
  validFrom?: string;
  validTo?: string;
  lastModifiedBy?: string;
  priority?: number;
  
  // Additional configuration fields
  currency?: string;
  minimumAmount?: number;
  maximumAmount?: number;
  conditions?: MarkupCondition[];
  tags?: string[];
  isDefault?: boolean;
  autoApply?: boolean;
}

// Filter interface for markup search
export interface MarkupFilters {
  search?: string;
  status?: MarkupStatus | '';
  type?: MarkupType | '';
  providerType?: ProviderType | '';
  region?: string;
  hotel?: string;
  provider?: string;
  validFrom?: string;
  validTo?: string;
  createdBy?: string;
  priority?: number;
}

// Response interfaces
export interface MarkupListResponse {
  data: Markup[];
  total: number;
  page: number;
  limit: number;
}

export interface MarkupResponse {
  data: Markup;
  message?: string;
}

// Error response interface
export interface MarkupErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

// Markup statistics interface
export interface MarkupStats {
  totalMarkups: number;
  activeMarkups: number;
  inactiveMarkups: number;
  averageMarkupValue: number;
  typeDistribution: Array<{
    type: MarkupType;
    count: number;
  }>;
  providerTypeDistribution: Array<{
    providerType: ProviderType;
    count: number;
  }>;
  topCreators: Array<{
    creator: string;
    count: number;
  }>;
}

// Markup application result interface
export interface MarkupApplicationResult {
  originalRate: number;
  finalRate: number;
  markupAmount: number;
  appliedMarkups: Array<{
    id: string;
    name: string;
    type: MarkupType;
    value: number;
    amount: number;
  }>;
  currency?: string;
}

// Markup validation rules
export const MarkupValidationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  value: {
    required: true,
    min: 0,
    max: 1000
  },
  priority: {
    required: true,
    min: 1,
    max: 100
  },
  minimumAmount: {
    min: 0
  },
  maximumAmount: {
    min: 0
  }
};

// Common regions
export const CommonRegions = [
  'Asia',
  'Europe',
  'North America',
  'South America',
  'Africa',
  'Oceania',
  'Middle East',
  'Caribbean',
  'Southeast Asia',
  'East Asia',
  'South Asia',
  'Central Asia',
  'Western Europe',
  'Eastern Europe',
  'Northern Europe',
  'Southern Europe'
];

// Common currencies
export const CommonCurrencies = [
  'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
  'MXN', 'SGD', 'HKD', 'NOK', 'KRW', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR'
];

// Markup condition operators
export const ConditionOperators = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'contains', label: 'Contains' },
  { value: 'in', label: 'In' },
  { value: 'not_in', label: 'Not In' }
];

// Utility functions for markup data manipulation
export const MarkupUtils = {
  // Format markup display name
  formatDisplayName: (markup: Markup): string => {
    return `${markup.name} (${markup.type === 'percentage' ? markup.value + '%' : '$' + markup.value})`;
  },

  // Get status badge color
  getStatusColor: (status: MarkupStatus): string => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Get type badge color
  getTypeColor: (type: MarkupType): string => {
    switch (type) {
      case 'percentage':
        return 'bg-blue-100 text-blue-800';
      case 'rate':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Get provider type badge color
  getProviderTypeColor: (providerType: ProviderType): string => {
    switch (providerType) {
      case 'common markup':
        return 'bg-yellow-100 text-yellow-800';
      case 'provider':
        return 'bg-indigo-100 text-indigo-800';
      case 'hotel':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Format markup value display
  formatValue: (markup: Markup): string => {
    if (markup.type === 'percentage') {
      return `${markup.value}%`;
    } else {
      return `$${markup.value.toFixed(2)}`;
    }
  },

  // Calculate markup amount
  calculateMarkupAmount: (markup: Markup, baseRate: number): number => {
    if (markup.type === 'percentage') {
      return (baseRate * markup.value) / 100;
    } else {
      return markup.value;
    }
  },

  // Apply markup to rate
  applyMarkup: (markup: Markup, baseRate: number): MarkupApplicationResult => {
    const markupAmount = MarkupUtils.calculateMarkupAmount(markup, baseRate);
    const finalRate = baseRate + markupAmount;

    return {
      originalRate: baseRate,
      finalRate,
      markupAmount,
      appliedMarkups: [{
        id: markup.id,
        name: markup.name,
        type: markup.type,
        value: markup.value,
        amount: markupAmount
      }],
      currency: markup.currency
    };
  },

  // Apply multiple markups
  applyMultipleMarkups: (markups: Markup[], baseRate: number): MarkupApplicationResult => {
    let currentRate = baseRate;
    const appliedMarkups: MarkupApplicationResult['appliedMarkups'] = [];
    let totalMarkupAmount = 0;

    // Sort markups by priority (lower number = higher priority)
    const sortedMarkups = [...markups].sort((a, b) => a.priority - b.priority);

    for (const markup of sortedMarkups) {
      if (markup.status === 'active' && MarkupUtils.isMarkupValid(markup)) {
        const markupAmount = MarkupUtils.calculateMarkupAmount(markup, currentRate);
        currentRate += markupAmount;
        totalMarkupAmount += markupAmount;

        appliedMarkups.push({
          id: markup.id,
          name: markup.name,
          type: markup.type,
          value: markup.value,
          amount: markupAmount
        });
      }
    }

    return {
      originalRate: baseRate,
      finalRate: currentRate,
      markupAmount: totalMarkupAmount,
      appliedMarkups
    };
  },

  // Check if markup is currently valid
  isMarkupValid: (markup: Markup): boolean => {
    const now = new Date();
    const validFrom = new Date(markup.validFrom);
    const validTo = markup.validTo ? new Date(markup.validTo) : null;

    return now >= validFrom && (!validTo || now <= validTo);
  },

  // Validate markup data
  validateMarkup: (markup: Partial<Markup>): string[] => {
    const errors: string[] = [];
    
    if (!markup.name || markup.name.length < 2) {
      errors.push('Markup name must be at least 2 characters long');
    }
    
    if (markup.value === undefined || markup.value < 0) {
      errors.push('Markup value must be a positive number');
    }
    
    if (markup.type === 'percentage' && markup.value && markup.value > 100) {
      errors.push('Percentage markup cannot exceed 100%');
    }
    
    if (!markup.priority || markup.priority < 1 || markup.priority > 100) {
      errors.push('Priority must be between 1 and 100');
    }
    
    if (markup.minimumAmount && markup.maximumAmount && markup.minimumAmount > markup.maximumAmount) {
      errors.push('Minimum amount cannot be greater than maximum amount');
    }
    
    if (!markup.validFrom) {
      errors.push('Valid from date is required');
    }
    
    if (markup.validFrom && markup.validTo && new Date(markup.validFrom) > new Date(markup.validTo)) {
      errors.push('Valid from date cannot be after valid to date');
    }
    
    return errors;
  },

  // Get markups applicable to specific criteria
  getApplicableMarkups: (markups: Markup[], criteria: {
    region?: string;
    hotel?: string;
    provider?: string;
    providerType?: ProviderType;
  }): Markup[] => {
    return markups.filter(markup => {
      if (markup.status !== 'active' || !MarkupUtils.isMarkupValid(markup)) {
        return false;
      }

      if (criteria.providerType && markup.providerType !== criteria.providerType) {
        return false;
      }

      if (criteria.region && markup.applicableRegions.length > 0 && !markup.applicableRegions.includes(criteria.region)) {
        return false;
      }

      if (criteria.hotel && markup.applicableHotels.length > 0 && !markup.applicableHotels.includes(criteria.hotel)) {
        return false;
      }

      if (criteria.provider && markup.applicableProviders.length > 0 && !markup.applicableProviders.includes(criteria.provider)) {
        return false;
      }

      return true;
    });
  }
};
