// Hotel provider status types
export type HotelProviderStatus = 'active' | 'inactive';

// Sync status types
export type SyncStatus = 'connected' | 'disconnected' | 'error';

// Main HotelProvider interface
export interface HotelProvider {
  id: string;
  name: string;
  apiKey: string;
  accountId: string;
  baseUrl: string;
  channelId: string;
  status: HotelProviderStatus;
  createdAt: string;
  updatedAt: string;
  description?: string;
  contactEmail?: string;
  supportPhone?: string;
  lastSyncAt?: string;
  syncStatus?: SyncStatus;
  
  // Additional configuration fields
  timeout?: number;
  retryAttempts?: number;
  rateLimit?: number;
  authType?: 'api_key' | 'oauth' | 'basic';
  webhookUrl?: string;
  isTestMode?: boolean;
  supportedCurrencies?: string[];
  supportedLanguages?: string[];
  features?: string[];
}

// Request interfaces for API operations
export interface CreateHotelProviderRequest {
  name: string;
  apiKey: string;
  accountId: string;
  baseUrl: string;
  channelId: string;
  status: HotelProviderStatus;
  description?: string;
  contactEmail?: string;
  supportPhone?: string;
  
  // Additional configuration fields
  timeout?: number;
  retryAttempts?: number;
  rateLimit?: number;
  authType?: 'api_key' | 'oauth' | 'basic';
  webhookUrl?: string;
  isTestMode?: boolean;
  supportedCurrencies?: string[];
  supportedLanguages?: string[];
  features?: string[];
}

export interface UpdateHotelProviderRequest {
  name?: string;
  apiKey?: string;
  accountId?: string;
  baseUrl?: string;
  channelId?: string;
  status?: HotelProviderStatus;
  description?: string;
  contactEmail?: string;
  supportPhone?: string;
  
  // Additional configuration fields
  timeout?: number;
  retryAttempts?: number;
  rateLimit?: number;
  authType?: 'api_key' | 'oauth' | 'basic';
  webhookUrl?: string;
  isTestMode?: boolean;
  supportedCurrencies?: string[];
  supportedLanguages?: string[];
  features?: string[];
}

// Filter interface for provider search
export interface HotelProviderFilters {
  search?: string;
  status?: HotelProviderStatus | '';
  syncStatus?: SyncStatus | '';
  authType?: 'api_key' | 'oauth' | 'basic' | '';
  isTestMode?: boolean;
}

// API Response interfaces (snake_case from backend)
export interface HotelProviderApiResponse {
  id?: string;
  name: string;
  api_key: string;
  account_id: string;
  base_url: string;
  channel_id: string;
  status?: HotelProviderStatus;
  created_at?: string;
  updated_at?: string;
  description?: string;
  contact_email?: string;
  support_phone?: string;
  last_sync_at?: string;
  sync_status?: SyncStatus;

  // Additional configuration fields
  timeout?: number;
  retry_attempts?: number;
  rate_limit?: number;
  auth_type?: 'api_key' | 'oauth' | 'basic';
  webhook_url?: string;
  is_test_mode?: boolean;
  supported_currencies?: string[];
  supported_languages?: string[];
  features?: string[];
}

// Response interfaces
export interface HotelProviderListResponse {
  data: HotelProvider[];
  total: number;
  page: number;
  limit: number;
}

export interface HotelProviderResponse {
  data: HotelProvider;
  message?: string;
}

// Error response interface
export interface HotelProviderErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

// Provider statistics interface
export interface HotelProviderStats {
  totalProviders: number;
  activeProviders: number;
  inactiveProviders: number;
  connectedProviders: number;
  disconnectedProviders: number;
  errorProviders: number;
  averageResponseTime: number;
  syncFrequency: Array<{
    provider: string;
    lastSync: string;
    frequency: number;
  }>;
}

// Connection test result interface
export interface ConnectionTestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  statusCode?: number;
  timestamp: string;
}

// Sync result interface
export interface SyncResult {
  success: boolean;
  message: string;
  syncedCount?: number;
  errorCount?: number;
  timestamp: string;
  details?: Array<{
    operation: string;
    status: 'success' | 'error';
    message: string;
  }>;
}

// Provider validation rules
export const HotelProviderValidationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  apiKey: {
    required: true,
    minLength: 10,
    maxLength: 200
  },
  accountId: {
    required: true,
    minLength: 3,
    maxLength: 50
  },
  baseUrl: {
    required: true,
    pattern: /^https?:\/\/.+/
  },
  channelId: {
    required: true,
    minLength: 3,
    maxLength: 50
  },
  contactEmail: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  supportPhone: {
    pattern: /^\+?[\d\s\-\(\)]+$/
  }
};

// Common provider features
export const CommonProviderFeatures = [
  'Real-time Availability',
  'Rate Management',
  'Inventory Management',
  'Booking Management',
  'Cancellation Support',
  'Modification Support',
  'Multi-currency Support',
  'Multi-language Support',
  'Webhook Support',
  'Batch Operations',
  'Rate Parity',
  'Channel Manager Integration',
  'PMS Integration',
  'Revenue Management',
  'Analytics & Reporting'
];

// Supported currencies
export const SupportedCurrencies = [
  'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
  'MXN', 'SGD', 'HKD', 'NOK', 'KRW', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR',
  'PLN', 'DKK', 'CZK', 'HUF', 'ILS', 'CLP', 'PHP', 'AED', 'COP', 'SAR',
  'MYR', 'RON', 'HRK', 'BGN', 'THB', 'IDR', 'EGP', 'QAR', 'KWD', 'BHD'
];

// Supported languages
export const SupportedLanguages = [
  'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
  'ar', 'hi', 'th', 'vi', 'id', 'ms', 'tl', 'tr', 'pl', 'nl',
  'sv', 'da', 'no', 'fi', 'cs', 'sk', 'hu', 'ro', 'bg', 'hr',
  'sl', 'et', 'lv', 'lt', 'mt', 'cy', 'is', 'mk', 'sq', 'sr'
];

// Utility functions for provider data manipulation
export const HotelProviderUtils = {
  // Format provider display name
  formatDisplayName: (provider: HotelProvider): string => {
    return `${provider.name} (${provider.channelId})`;
  },

  // Get status badge color
  getStatusColor: (status: HotelProviderStatus): string => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Get sync status badge color
  getSyncStatusColor: (syncStatus?: SyncStatus): string => {
    switch (syncStatus) {
      case 'connected':
        return 'bg-green-100 text-green-800';
      case 'disconnected':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  },

  // Format last sync time
  formatLastSync: (lastSyncAt?: string): string => {
    if (!lastSyncAt) return 'Never';
    
    const syncDate = new Date(lastSyncAt);
    const now = new Date();
    const diffMs = now.getTime() - syncDate.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return syncDate.toLocaleDateString();
  },

  // Validate provider data
  validateProvider: (provider: Partial<HotelProvider>): string[] => {
    const errors: string[] = [];
    
    if (!provider.name || provider.name.length < 2) {
      errors.push('Provider name must be at least 2 characters long');
    }
    
    if (!provider.apiKey || provider.apiKey.length < 10) {
      errors.push('API key must be at least 10 characters long');
    }
    
    if (!provider.accountId || provider.accountId.length < 3) {
      errors.push('Account ID must be at least 3 characters long');
    }
    
    if (!provider.baseUrl || !HotelProviderValidationRules.baseUrl.pattern.test(provider.baseUrl)) {
      errors.push('Base URL must be a valid HTTP/HTTPS URL');
    }
    
    if (!provider.channelId || provider.channelId.length < 3) {
      errors.push('Channel ID must be at least 3 characters long');
    }
    
    if (provider.contactEmail && !HotelProviderValidationRules.contactEmail.pattern.test(provider.contactEmail)) {
      errors.push('Invalid email format');
    }
    
    if (provider.supportPhone && !HotelProviderValidationRules.supportPhone.pattern.test(provider.supportPhone)) {
      errors.push('Invalid phone number format');
    }
    
    return errors;
  },

  // Mask sensitive data for display
  maskApiKey: (apiKey: string): string => {
    if (apiKey.length <= 8) return '*'.repeat(apiKey.length);
    return apiKey.substring(0, 4) + '*'.repeat(apiKey.length - 8) + apiKey.substring(apiKey.length - 4);
  },

  // Check if provider supports feature
  supportsFeature: (provider: HotelProvider, feature: string): boolean => {
    return provider.features?.includes(feature) || false;
  },

  // Map API response to frontend model
  mapApiResponseToModel: (apiResponse: HotelProviderApiResponse): HotelProvider => {
    return {
      id: apiResponse.id || '',
      name: apiResponse.name,
      apiKey: apiResponse.api_key,
      accountId: apiResponse.account_id,
      baseUrl: apiResponse.base_url,
      channelId: apiResponse.channel_id,
      status: apiResponse.status || 'inactive',
      createdAt: apiResponse.created_at || new Date().toISOString(),
      updatedAt: apiResponse.updated_at || new Date().toISOString(),
      description: apiResponse.description,
      contactEmail: apiResponse.contact_email,
      supportPhone: apiResponse.support_phone,
      lastSyncAt: apiResponse.last_sync_at,
      syncStatus: apiResponse.sync_status,
      timeout: apiResponse.timeout,
      retryAttempts: apiResponse.retry_attempts,
      rateLimit: apiResponse.rate_limit,
      authType: apiResponse.auth_type,
      webhookUrl: apiResponse.webhook_url,
      isTestMode: apiResponse.is_test_mode,
      supportedCurrencies: apiResponse.supported_currencies,
      supportedLanguages: apiResponse.supported_languages,
      features: apiResponse.features
    };
  },

  // Map frontend model to API request
  mapModelToApiRequest: (provider: Partial<HotelProvider>): Partial<HotelProviderApiResponse> => {
    return {
      id: provider.id,
      name: provider.name || '',
      api_key: provider.apiKey || '',
      account_id: provider.accountId || '',
      base_url: provider.baseUrl || '',
      channel_id: provider.channelId || '',
      status: provider.status,
      created_at: provider.createdAt,
      updated_at: provider.updatedAt,
      description: provider.description,
      contact_email: provider.contactEmail,
      support_phone: provider.supportPhone,
      last_sync_at: provider.lastSyncAt,
      sync_status: provider.syncStatus,
      timeout: provider.timeout,
      retry_attempts: provider.retryAttempts,
      rate_limit: provider.rateLimit,
      auth_type: provider.authType,
      webhook_url: provider.webhookUrl,
      is_test_mode: provider.isTestMode,
      supported_currencies: provider.supportedCurrencies,
      supported_languages: provider.supportedLanguages,
      features: provider.features
    };
  },

  // Get provider health score
  getHealthScore: (provider: HotelProvider): number => {
    let score = 0;
    
    // Status check (40 points)
    if (provider.status === 'active') score += 40;
    
    // Sync status check (30 points)
    if (provider.syncStatus === 'connected') score += 30;
    else if (provider.syncStatus === 'disconnected') score += 15;
    
    // Recent sync check (20 points)
    if (provider.lastSyncAt) {
      const lastSync = new Date(provider.lastSyncAt);
      const now = new Date();
      const hoursSinceSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceSync < 1) score += 20;
      else if (hoursSinceSync < 24) score += 15;
      else if (hoursSinceSync < 168) score += 10; // 1 week
      else score += 5;
    }
    
    // Configuration completeness (10 points)
    const requiredFields = ['contactEmail', 'supportPhone', 'description'];
    const completedFields = requiredFields.filter(field => provider[field as keyof HotelProvider]);
    score += Math.floor((completedFields.length / requiredFields.length) * 10);
    
    return Math.min(score, 100);
  }
};
