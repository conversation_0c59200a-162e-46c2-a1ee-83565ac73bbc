'use client';

import { useState, useEffect } from 'react';
import { CancellationStats } from '../types';

export default function CancellationStats() {
  const [stats, setStats] = useState<CancellationStats>({
    totalCancellations: 0,
    pendingCancellations: 0,
    processedCancellations: 0,
    completedCancellations: 0,
    totalRefundAmount: 0,
    averageRefundAmount: 0,
    cancellationRate: 0,
    refundRate: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const fetchStats = async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockStats: CancellationStats = {
        totalCancellations: 156,
        pendingCancellations: 23,
        processedCancellations: 89,
        completedCancellations: 44,
        totalRefundAmount: 45670.50,
        averageRefundAmount: 292.76,
        cancellationRate: 12.5,
        refundRate: 85.2
      };
      
      setStats(mockStats);
      setLoading(false);
    };

    fetchStats();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Cancellations */}
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-blue-700">Total Cancellations</p>
            <p className="text-3xl font-bold text-blue-900">{stats.totalCancellations.toLocaleString()}</p>
            <p className="text-xs text-blue-600 mt-1">
              Rate: {formatPercentage(stats.cancellationRate)}
            </p>
          </div>
          <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
            <i className="ri-close-circle-line text-white text-xl"></i>
          </div>
        </div>
      </div>

      {/* Pending Cancellations */}
      <div className="bg-gradient-to-r from-amber-50 to-amber-100 rounded-xl p-6 border border-amber-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-amber-700">Pending Review</p>
            <p className="text-3xl font-bold text-amber-900">{stats.pendingCancellations}</p>
            <p className="text-xs text-amber-600 mt-1">
              Requires attention
            </p>
          </div>
          <div className="w-12 h-12 bg-amber-500 rounded-lg flex items-center justify-center">
            <i className="ri-time-line text-white text-xl"></i>
          </div>
        </div>
      </div>

      {/* Processed Cancellations */}
      <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-green-700">Processed</p>
            <p className="text-3xl font-bold text-green-900">{stats.processedCancellations}</p>
            <p className="text-xs text-green-600 mt-1">
              Completed: {stats.completedCancellations}
            </p>
          </div>
          <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
            <i className="ri-check-line text-white text-xl"></i>
          </div>
        </div>
      </div>

      {/* Total Refund Amount */}
      <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-purple-700">Total Refunds</p>
            <p className="text-3xl font-bold text-purple-900">{formatCurrency(stats.totalRefundAmount)}</p>
            <p className="text-xs text-purple-600 mt-1">
              Avg: {formatCurrency(stats.averageRefundAmount)}
            </p>
          </div>
          <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
            <i className="ri-refund-line text-white text-xl"></i>
          </div>
        </div>
      </div>


    </div>
  );
}
