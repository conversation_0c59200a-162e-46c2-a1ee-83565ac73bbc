
'use client';

import { useState, useEffect } from 'react';
import { Hotel, Pagination as PaginationData } from '../../../models/hotel.model';
import { usePagination } from '../../../../hooks/usePagination';
import Pagination from '../../../../styles/components/Pagination';

interface HotelListProps {
  hotels: Hotel[];
  onEdit: (hotel: Hotel) => void;
  onView: (hotel: Hotel) => void;
  pagination?: PaginationData | null;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  loading?: boolean;
}

export default function HotelList({
  hotels = [],
  onEdit,
  onView,
  pagination: paginationData,
  currentPage,
  onPageChange,
  onPageSizeChange,
  loading = false
}: HotelListProps) {
  const [sortField, setSortField] = useState<keyof Hotel>('updated_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Initialize local pagination for client-side sorting (fallback)
  const localPagination = usePagination<Hotel>({ initialItemsPerPage: 5 });

  // Update local pagination data when hotels change (for client-side sorting)
  useEffect(() => {
    if (!paginationData && hotels) {
      // Use local pagination for client-side sorting when no server pagination
      const sortedHotels = [...hotels].sort((a, b) => {
        const aValue = a[sortField];
        const bValue = b[sortField];

        // Handle undefined values
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === undefined) return sortDirection === 'asc' ? -1 : 1;

        // Handle string comparison
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        // Handle number comparison
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
        }

        // Fallback to string comparison
        const aStr = String(aValue || '');
        const bStr = String(bValue || '');
        return sortDirection === 'asc'
          ? aStr.localeCompare(bStr)
          : bStr.localeCompare(aStr);
      });

      localPagination.setPaginationData(sortedHotels);
    }
  }, [hotels, sortField, sortDirection, paginationData]); // Removed localPagination to prevent infinite loop

  const handleSort = (field: keyof Hotel) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getStatusBadge = (status?: Hotel['status']) => {
    if (!status) {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>;
    }

    const statusConfig = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig[status] || 'bg-gray-100 text-gray-800'}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getStarRating = (hotel: Hotel) => {
    try {
      // Handle both new API format (rating_score as string) and legacy format
      const numericRating = hotel?.starRating || (hotel?.rating_score ? parseFloat(hotel.rating_score) : 0);
      const displayRating = Math.round(numericRating);

      return (
        <div className="flex items-center">
          {[...Array(5)].map((_, i) => (
            <i
              key={i}
              className={`ri-star-${i < displayRating ? 'fill' : 'line'} text-yellow-400 text-sm`}
            ></i>
          ))}
          <span className="ml-1 text-sm text-gray-600">({numericRating.toFixed(1)})</span>
        </div>
      );
    } catch (error) {
      console.error('Error rendering star rating:', error);
      return <span className="text-sm text-gray-500">N/A</span>;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {(loading || hotels.length > 0) && (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  Hotel Name
                  {sortField === 'name' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('country')}
              >
                <div className="flex items-center">
                  Location
                  {sortField === 'country' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('starRating')}
              >
                <div className="flex items-center">
                  Rating
                  {sortField === 'starRating' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type & ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  Status
                  {sortField === 'status' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('updated_at')}
              >
                <div className="flex items-center">
                  Last Updated
                  {sortField === 'updated_at' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              // Loading state
              Array.from({ length: 5 }).map((_, index) => (
                <tr key={`loading-${index}`} className="animate-pulse">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-lg"></div>
                      <div className="ml-4">
                        <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                    </div>
                  </td>
                </tr>
              ))
            ) : (paginationData ? hotels : localPagination.paginatedData).filter(Boolean).map((hotel) => (
              <tr key={hotel.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <img
                        className="h-10 w-10 rounded-lg object-cover"
                        src={`https://readdy.ai/api/search-image?query=luxury%20hotel%20exterior%20architecture%20modern%20building%20facade&width=40&height=40&seq=${hotel.id}&orientation=squarish`}
                        alt={hotel.name}
                      />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{hotel.name}</div>
                      <div className="text-sm text-gray-500 flex items-center space-x-2">
                        <span>{hotel.type || hotel.hotelType || 'N/A'}</span>
                        {hotel.provider_name && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {hotel.provider_name}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{hotel.city}, {hotel.country}</div>
                  <div className="text-sm text-gray-500">
                    {hotel.address || hotel.area || 'N/A'}
                  </div>
                  {hotel.country_code && (
                    <div className="text-xs text-gray-400 mt-1">
                      {hotel.country_code}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStarRating(hotel)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{hotel.type || hotel.hotelType || 'N/A'}</div>
                  <div className="text-sm text-gray-500 space-y-1">
                    {hotel.contractType && (
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        hotel.contractType === 'static' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                      }`}>
                        {hotel.contractType}
                      </span>
                    )}
                    {hotel.hotel_id && (
                      <div className="text-xs text-gray-400">
                        ID: {hotel.hotel_id}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {hotel.phones_json && hotel.phones_json.length > 0 ? (
                      <div className="flex items-center">
                        <i className="ri-phone-line text-gray-400 mr-1"></i>
                        <span>{hotel.phones_json[0]}</span>
                      </div>
                    ) : (
                      <span className="text-gray-400">No phone</span>
                    )}
                  </div>
                  {hotel.faxes_json && hotel.faxes_json.length > 0 && (
                    <div className="text-xs text-gray-500 flex items-center mt-1">
                      <i className="ri-printer-line text-gray-400 mr-1"></i>
                      <span>Fax available</span>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(hotel.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {hotel.updated_at ? new Date(hotel.updated_at).toLocaleDateString() :
                   hotel.updatedAt ? new Date(hotel.updatedAt).toLocaleDateString() : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onView(hotel)}
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <i className="ri-eye-line"></i>
                    </button>
                    <button
                      onClick={() => onEdit(hotel)}
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Edit Hotel"
                    >
                      <i className="ri-edit-line"></i>
                    </button>
                    <button
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete Hotel"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        </div>
      )}

      {!loading && (!hotels || hotels.length === 0) && (
        <div className="text-center py-12">
          <i className="ri-hotel-line text-gray-300 text-4xl mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hotels found</h3>
          <p className="text-gray-500">Get started by adding your first hotel listing.</p>
        </div>
      )}

      {/* Pagination */}
      {hotels && hotels.length > 0 && (
        <div className="mt-6">
          {paginationData && onPageChange && onPageSizeChange ? (
            // Server-side pagination
            <Pagination
              currentPage={currentPage || 1}
              totalPages={paginationData.total_pages}
              itemsPerPage={paginationData.page_size}
              totalItems={paginationData.total_items}
              onPageChange={onPageChange}
              onItemsPerPageChange={onPageSizeChange}
            />
          ) : (
            // Client-side pagination (fallback)
            <Pagination
              currentPage={localPagination.currentPage}
              totalPages={localPagination.totalPages}
              itemsPerPage={localPagination.itemsPerPage}
              totalItems={localPagination.totalItems}
              onPageChange={localPagination.setCurrentPage}
              onItemsPerPageChange={localPagination.setItemsPerPage}
            />
          )}
        </div>
      )}
    </div>
  );
}
