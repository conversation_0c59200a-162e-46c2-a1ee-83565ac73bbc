import axiosInstance from "../../api/axiosInstance";
import {
  HotelProvider,
  CreateHotelProviderRequest,
  UpdateHotelProviderRequest,
  HotelProviderApiResponse,
  HotelProviderUtils
} from "../models/hotel-provider.model";

// Get all hotel providers
export const getAllHotelProviders = async (retries = 3): Promise<HotelProvider[]> => {
  try {
    const response = await axiosInstance.get<HotelProviderApiResponse[]>('/api/v1/providers');
    // Map API response to frontend model
    return response.data.map(apiProvider => HotelProviderUtils.mapApiResponseToModel(apiProvider));
  } catch (error: any) {
    console.error('Error fetching hotel providers:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getAllHotelProviders(retries - 1);
    }

    throw error;
  }
};

// Get hotel provider by ID
export const getHotelProviderById = async (id: string, retries = 3): Promise<HotelProvider> => {
  try {
    const response = await axiosInstance.get<HotelProviderApiResponse>(`/providers/${id}/`);
    // Map API response to frontend model
    return HotelProviderUtils.mapApiResponseToModel(response.data);
  } catch (error: any) {
    console.error('Error fetching hotel provider by ID:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getHotelProviderById(id, retries - 1);
    }

    throw error;
  }
};

// Create new hotel provider
export const createHotelProvider = async (providerData: CreateHotelProviderRequest, retries = 3): Promise<HotelProvider> => {
  try {
    // Map frontend model to API request format
    const apiRequest = HotelProviderUtils.mapModelToApiRequest(providerData);
    const response = await axiosInstance.post<HotelProviderApiResponse>('/providers/', apiRequest);
    // Map API response back to frontend model
    return HotelProviderUtils.mapApiResponseToModel(response.data);
  } catch (error: any) {
    console.error('Error creating hotel provider:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createHotelProvider(providerData, retries - 1);
    }

    throw error;
  }
};

// Update hotel provider
export const updateHotelProvider = async (id: string, providerData: UpdateHotelProviderRequest, retries = 3): Promise<HotelProvider> => {
  try {
    // Map frontend model to API request format
    const apiRequest = HotelProviderUtils.mapModelToApiRequest(providerData);
    const response = await axiosInstance.put<HotelProviderApiResponse>(`/providers/${id}/`, apiRequest);
    // Map API response back to frontend model
    return HotelProviderUtils.mapApiResponseToModel(response.data);
  } catch (error: any) {
    console.error('Error updating hotel provider:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateHotelProvider(id, providerData, retries - 1);
    }

    throw error;
  }
};

// Delete hotel provider
export const deleteHotelProvider = async (id: string, retries = 3): Promise<void> => {
  try {
    await axiosInstance.delete(`/providers/${id}/`);
  } catch (error: any) {
    console.error('Error deleting hotel provider:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return deleteHotelProvider(id, retries - 1);
    }

    throw error;
  }
};

// Update provider status
export const updateProviderStatus = async (id: string, status: HotelProvider['status'], retries = 3): Promise<HotelProvider> => {
  try {
    const response = await axiosInstance.patch<HotelProviderApiResponse>(`/providers/${id}/status/`, { status });
    // Map API response back to frontend model
    return HotelProviderUtils.mapApiResponseToModel(response.data);
  } catch (error: any) {
    console.error('Error updating provider status:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateProviderStatus(id, status, retries - 1);
    }

    throw error;
  }
};

// Test provider connection
export const testProviderConnection = async (id: string, retries = 3): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await axiosInstance.post<{ success: boolean; message: string }>(`/providers/${id}/test-connection/`);
    return response.data;
  } catch (error: any) {
    console.error('Error testing provider connection:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return testProviderConnection(id, retries - 1);
    }

    return { success: false, message: 'Connection test failed' };
  }
};

// Sync provider data
export const syncProviderData = async (id: string, retries = 3): Promise<{ success: boolean; message: string; syncedCount?: number }> => {
  try {
    const response = await axiosInstance.post<{ success: boolean; message: string; syncedCount?: number }>(`/providers/${id}/sync/`);
    return response.data;
  } catch (error: any) {
    console.error('Error syncing provider data:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return syncProviderData(id, retries - 1);
    }

    return { success: false, message: 'Data sync failed' };
  }
};

// Search providers
export const searchHotelProviders = async (query: string, filters?: any, retries = 3): Promise<HotelProvider[]> => {
  try {
    const params = new URLSearchParams();
    if (query) params.append('search', query);
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key]) params.append(key, filters[key]);
      });
    }

    const response = await axiosInstance.get<HotelProviderApiResponse[]>(`/providers/search/?${params.toString()}`);
    // Map API response to frontend model
    return response.data.map(apiProvider => HotelProviderUtils.mapApiResponseToModel(apiProvider));
  } catch (error: any) {
    console.error('Error searching hotel providers:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return searchHotelProviders(query, filters, retries - 1);
    }

    throw error;
  }
};


 