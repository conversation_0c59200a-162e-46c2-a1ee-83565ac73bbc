export interface Booking {
  id: string;
  bookingId: string; // Display booking ID (e.g., BK-2024-001)
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  hotelId: string;
  hotelName: string;
  roomId: string;
  roomName: string;
  checkInDate: string;
  checkOutDate: string;
  numberOfGuests: number;
  numberOfNights: number;
  bookingStatus: BookingStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  totalAmount: number;
  amountPaid: number;
  amountDue: number;
  currency: string;
  bookingDate: string;
  createdAt: string;
  updatedAt: string;
  specialRequests?: string;
  cancellationReason?: string;
  cancellationDate?: string;
  refundAmount?: number;
  guestDetails?: GuestDetails;
  paymentDetails?: PaymentDetails;
  bookingSource: BookingSource;
  confirmationNumber?: string;
  notes?: string;
}

export interface GuestDetails {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };
  nationality?: string;
  dateOfBirth?: string;
  idType?: string;
  idNumber?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface PaymentDetails {
  transactionId?: string;
  paymentGateway?: string;
  cardLast4?: string;
  cardType?: string;
  paymentDate?: string;
  refundTransactionId?: string;
  refundDate?: string;
  paymentReference?: string;
}

export type BookingStatus = 
  | 'confirmed' 
  | 'pending' 
  | 'checked-in' 
  | 'checked-out' 
  | 'cancelled' 
  | 'no-show'
  | 'completed';

export type PaymentStatus = 
  | 'paid' 
  | 'partial' 
  | 'pending' 
  | 'failed' 
  | 'refunded' 
  | 'cancelled';

export type PaymentMethod = 
  | 'credit-card' 
  | 'debit-card' 
  | 'bank-transfer' 
  | 'cash' 
  | 'paypal' 
  | 'stripe' 
  | 'razorpay' 
  | 'other';

export type BookingSource = 
  | 'direct' 
  | 'booking.com' 
  | 'expedia' 
  | 'agoda' 
  | 'airbnb' 
  | 'phone' 
  | 'walk-in' 
  | 'agent' 
  | 'other';

// Statistics interface for booking cards
export interface BookingStats {
  totalBookings: number;
  completedBookings: number;
  upcomingBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  occupancyRate: number;
  cancellationRate: number;
}

// Filter interface for booking list
export interface BookingFilters {
  search: string;
  bookingStatus: BookingStatus | '';
  paymentStatus: PaymentStatus | '';
  paymentMethod: PaymentMethod | '';
  dateRange: {
    startDate: string;
    endDate: string;
  };
  hotelId: string;
  bookingSource: BookingSource | '';
}

// Form data interface for add/edit
export interface BookingFormData {
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  hotelId: string;
  roomId: string;
  checkInDate: string;
  checkOutDate: string;
  numberOfGuests: number;
  bookingStatus: BookingStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  totalAmount: number;
  amountPaid: number;
  currency: string;
  specialRequests: string;
  bookingSource: BookingSource;
  notes: string;
  guestDetails: GuestDetails;
  paymentDetails: PaymentDetails;
}

// Cancellation related types
export type CancellationStatus =
  | 'pending'
  | 'approved'
  | 'rejected'
  | 'processed'
  | 'completed'
  | 'cancelled';

export type RefundStatus =
  | 'not-applicable'
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'partial'
  | 'rejected';

export type CancellationType =
  | 'full-cancellation'
  | 'partial-cancellation'
  | 'modification';

export type CancellationReason =
  | 'guest-request'
  | 'medical-emergency'
  | 'travel-restrictions'
  | 'weather-conditions'
  | 'hotel-issue'
  | 'overbooking'
  | 'no-show'
  | 'duplicate-booking'
  | 'payment-failure'
  | 'force-majeure'
  | 'other';

export type RefundMethod =
  | 'original-payment'
  | 'bank-transfer'
  | 'cash'
  | 'credit'
  | 'voucher'
  | 'other';

export type RequestedBy =
  | 'guest'
  | 'hotel'
  | 'admin'
  | 'system';

export interface Cancellation {
  id: string;
  cancellationId: string;
  bookingId: string;
  bookingReference: string;
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  hotelId: string;
  hotelName: string;
  roomId: string;
  roomName: string;
  originalCheckInDate: string;
  originalCheckOutDate: string;
  numberOfGuests: number;
  numberOfNights: number;
  cancellationDate: string;
  cancellationReason: CancellationReason;
  cancellationStatus: CancellationStatus;
  cancellationType: CancellationType;
  originalAmount: number;
  refundAmount: number;
  cancellationFee: number;
  refundStatus: RefundStatus;
  refundMethod: RefundMethod;
  currency: string;
  cancellationPolicy: string;
  requestedBy: RequestedBy;
  processedBy?: string;
  cancellationNotes?: string;
  refundNotes?: string;
  createdAt: string;
  updatedAt: string;
  refundDate?: string;
  refundTransactionId?: string;
  guestDetails?: GuestDetails;
}

export interface CancellationStats {
  totalCancellations: number;
  pendingCancellations: number;
  processedCancellations: number;
  completedCancellations: number;
  totalRefundAmount: number;
  averageRefundAmount: number;
  cancellationRate: number;
  refundRate: number;
}

export interface CancellationFilters {
  search: string;
  cancellationStatus: string;
  refundStatus: string;
  cancellationReason: string;
  cancellationType: string;
  requestedBy: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  hotelId: string;
  refundMethod: string;
}

// Helper functions for cancellation types
export const getCancellationStatusText = (status: CancellationStatus): string => {
  const statusMap = {
    'pending': 'Pending',
    'approved': 'Approved',
    'rejected': 'Rejected',
    'processed': 'Processed',
    'completed': 'Completed',
    'cancelled': 'Cancelled'
  };
  return statusMap[status] || status;
};

export const getRefundStatusText = (status: RefundStatus): string => {
  const statusMap = {
    'not-applicable': 'Not Applicable',
    'pending': 'Pending',
    'processing': 'Processing',
    'completed': 'Completed',
    'failed': 'Failed',
    'partial': 'Partial',
    'rejected': 'Rejected'
  };
  return statusMap[status] || status;
};

export const getCancellationReasonText = (reason: CancellationReason): string => {
  const reasonMap = {
    'guest-request': 'Guest Request',
    'medical-emergency': 'Medical Emergency',
    'travel-restrictions': 'Travel Restrictions',
    'weather-conditions': 'Weather Conditions',
    'hotel-issue': 'Hotel Issue',
    'overbooking': 'Overbooking',
    'no-show': 'No Show',
    'duplicate-booking': 'Duplicate Booking',
    'payment-failure': 'Payment Failure',
    'force-majeure': 'Force Majeure',
    'other': 'Other'
  };
  return reasonMap[reason] || reason;
};
